export type Priority = 'low' | 'medium' | 'high' | 'urgent';

export type TodoStatus = 'pending' | 'in-progress' | 'completed' | 'cancelled';

export interface Tag {
  id: string;
  name: string;
  color: string;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  icon?: string;
}

export interface Todo {
  id: string;
  title: string;
  description?: string;
  status: TodoStatus;
  priority: Priority;
  categoryId?: string;
  tags: string[]; // Tag IDs
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  order: number;
  isArchived: boolean;
  subtasks: Subtask[];
  attachments: Attachment[];
  reminders: Reminder[];
}

export interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date;
  order: number;
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: Date;
}

export interface Reminder {
  id: string;
  datetime: Date;
  message?: string;
  isActive: boolean;
}

export interface TodoFilter {
  status?: TodoStatus[];
  priority?: Priority[];
  categoryId?: string;
  tagIds?: string[];
  dueDateRange?: {
    start?: Date;
    end?: Date;
  };
  searchQuery?: string;
  showArchived?: boolean;
}

export interface TodoSort {
  field: 'title' | 'createdAt' | 'updatedAt' | 'dueDate' | 'priority' | 'order';
  direction: 'asc' | 'desc';
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  defaultView: 'list' | 'grid' | 'kanban';
  showCompletedTasks: boolean;
  enableNotifications: boolean;
  autoArchiveCompleted: boolean;
  autoArchiveDays: number;
  defaultPriority: Priority;
  workingHours: {
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
}

export interface AppState {
  todos: Todo[];
  categories: Category[];
  tags: Tag[];
  filter: TodoFilter;
  sort: TodoSort;
  settings: AppSettings;
  selectedTodos: string[];
  isLoading: boolean;
  error: string | null;
}

// Action types for state management
export type TodoAction =
  | { type: 'ADD_TODO'; payload: Omit<Todo, 'id' | 'createdAt' | 'updatedAt'> }
  | { type: 'UPDATE_TODO'; payload: { id: string; updates: Partial<Todo> } }
  | { type: 'DELETE_TODO'; payload: string }
  | { type: 'TOGGLE_TODO'; payload: string }
  | { type: 'REORDER_TODOS'; payload: { sourceIndex: number; destinationIndex: number } }
  | { type: 'BULK_UPDATE_TODOS'; payload: { ids: string[]; updates: Partial<Todo> } }
  | { type: 'ADD_CATEGORY'; payload: Omit<Category, 'id'> }
  | { type: 'UPDATE_CATEGORY'; payload: { id: string; updates: Partial<Category> } }
  | { type: 'DELETE_CATEGORY'; payload: string }
  | { type: 'ADD_TAG'; payload: Omit<Tag, 'id'> }
  | { type: 'UPDATE_TAG'; payload: { id: string; updates: Partial<Tag> } }
  | { type: 'DELETE_TAG'; payload: string }
  | { type: 'SET_FILTER'; payload: Partial<TodoFilter> }
  | { type: 'CLEAR_FILTER' }
  | { type: 'SET_SORT'; payload: TodoSort }
  | { type: 'SET_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'SELECT_TODOS'; payload: string[] }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOAD_DATA'; payload: { todos: Todo[]; categories: Category[]; tags: Tag[] } };

// Utility types
export type CreateTodoInput = Omit<Todo, 'id' | 'createdAt' | 'updatedAt' | 'order' | 'subtasks' | 'attachments' | 'reminders'>;
export type UpdateTodoInput = Partial<Omit<Todo, 'id' | 'createdAt'>>;
