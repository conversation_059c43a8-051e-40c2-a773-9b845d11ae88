import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Todo, Category, Tag, TodoFilter, TodoSort, AppSettings, Priority, TodoStatus } from '@/types/todo';
import { generateId } from '@/lib/utils';

interface TodoStore {
  // State
  todos: Todo[];
  categories: Category[];
  tags: Tag[];
  filter: TodoFilter;
  sort: TodoSort;
  settings: AppSettings;
  selectedTodos: string[];
  isLoading: boolean;
  error: string | null;

  // Todo actions
  addTodo: (todo: Omit<Todo, 'id' | 'createdAt' | 'updatedAt' | 'order'>) => void;
  updateTodo: (id: string, updates: Partial<Todo>) => void;
  deleteTodo: (id: string) => void;
  toggleTodo: (id: string) => void;
  reorderTodos: (sourceIndex: number, destinationIndex: number) => void;
  bulkUpdateTodos: (ids: string[], updates: Partial<Todo>) => void;
  duplicateTodo: (id: string) => void;
  archiveTodo: (id: string) => void;
  unarchiveTodo: (id: string) => void;

  // Category actions
  addCategory: (category: Omit<Category, 'id'>) => void;
  updateCategory: (id: string, updates: Partial<Category>) => void;
  deleteCategory: (id: string) => void;

  // Tag actions
  addTag: (tag: Omit<Tag, 'id'>) => void;
  updateTag: (id: string, updates: Partial<Tag>) => void;
  deleteTag: (id: string) => void;

  // Filter and sort actions
  setFilter: (filter: Partial<TodoFilter>) => void;
  clearFilter: () => void;
  setSort: (sort: TodoSort) => void;

  // Settings actions
  updateSettings: (settings: Partial<AppSettings>) => void;

  // Selection actions
  selectTodos: (ids: string[]) => void;
  toggleTodoSelection: (id: string) => void;
  clearSelection: () => void;
  selectAll: () => void;

  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  importData: (data: { todos: Todo[]; categories: Category[]; tags: Tag[] }) => void;
  exportData: () => { todos: Todo[]; categories: Category[]; tags: Tag[] };
  resetStore: () => void;
}

const defaultSettings: AppSettings = {
  theme: 'system',
  defaultView: 'list',
  showCompletedTasks: true,
  enableNotifications: true,
  autoArchiveCompleted: false,
  autoArchiveDays: 30,
  defaultPriority: 'medium',
  workingHours: {
    start: '09:00',
    end: '18:00',
  },
};

const defaultFilter: TodoFilter = {
  showArchived: false,
};

const defaultSort: TodoSort = {
  field: 'order',
  direction: 'asc',
};

export const useTodoStore = create<TodoStore>()(
  persist(
    (set, get) => ({
      // Initial state
      todos: [],
      categories: [
        { id: 'work', name: '工作', color: '#3b82f6', icon: '💼' },
        { id: 'personal', name: '个人', color: '#10b981', icon: '🏠' },
        { id: 'shopping', name: '购物', color: '#f59e0b', icon: '🛒' },
        { id: 'health', name: '健康', color: '#ef4444', icon: '❤️' },
      ],
      tags: [
        { id: 'urgent', name: '紧急', color: '#ef4444' },
        { id: 'important', name: '重要', color: '#f59e0b' },
        { id: 'meeting', name: '会议', color: '#3b82f6' },
        { id: 'review', name: '复习', color: '#8b5cf6' },
      ],
      filter: defaultFilter,
      sort: defaultSort,
      settings: defaultSettings,
      selectedTodos: [],
      isLoading: false,
      error: null,

      // Todo actions
      addTodo: (todoData) => {
        const now = new Date();
        const todos = get().todos;
        const maxOrder = Math.max(...todos.map(t => t.order), 0);
        
        const newTodo: Todo = {
          ...todoData,
          id: generateId(),
          createdAt: now,
          updatedAt: now,
          order: maxOrder + 1,
          subtasks: todoData.subtasks || [],
          attachments: todoData.attachments || [],
          reminders: todoData.reminders || [],
        };

        set(state => ({
          todos: [...state.todos, newTodo],
        }));
      },

      updateTodo: (id, updates) => {
        set(state => ({
          todos: state.todos.map(todo =>
            todo.id === id
              ? { ...todo, ...updates, updatedAt: new Date() }
              : todo
          ),
        }));
      },

      deleteTodo: (id) => {
        set(state => ({
          todos: state.todos.filter(todo => todo.id !== id),
          selectedTodos: state.selectedTodos.filter(selectedId => selectedId !== id),
        }));
      },

      toggleTodo: (id) => {
        set(state => ({
          todos: state.todos.map(todo => {
            if (todo.id === id) {
              const newStatus: TodoStatus = todo.status === 'completed' ? 'pending' : 'completed';
              return {
                ...todo,
                status: newStatus,
                completedAt: newStatus === 'completed' ? new Date() : undefined,
                updatedAt: new Date(),
              };
            }
            return todo;
          }),
        }));
      },

      reorderTodos: (sourceIndex, destinationIndex) => {
        set(state => {
          const todos = [...state.todos];
          const [removed] = todos.splice(sourceIndex, 1);
          todos.splice(destinationIndex, 0, removed);
          
          // Update order values
          const updatedTodos = todos.map((todo, index) => ({
            ...todo,
            order: index,
            updatedAt: new Date(),
          }));

          return { todos: updatedTodos };
        });
      },

      bulkUpdateTodos: (ids, updates) => {
        set(state => ({
          todos: state.todos.map(todo =>
            ids.includes(todo.id)
              ? { ...todo, ...updates, updatedAt: new Date() }
              : todo
          ),
        }));
      },

      duplicateTodo: (id) => {
        const todo = get().todos.find(t => t.id === id);
        if (todo) {
          const { id: _, createdAt: __, updatedAt: ___, completedAt: ____, ...todoData } = todo;
          get().addTodo({
            ...todoData,
            title: `${todo.title} (副本)`,
            status: 'pending',
          });
        }
      },

      archiveTodo: (id) => {
        get().updateTodo(id, { isArchived: true });
      },

      unarchiveTodo: (id) => {
        get().updateTodo(id, { isArchived: false });
      },

      // Category actions
      addCategory: (categoryData) => {
        const newCategory: Category = {
          ...categoryData,
          id: generateId(),
        };
        set(state => ({
          categories: [...state.categories, newCategory],
        }));
      },

      updateCategory: (id, updates) => {
        set(state => ({
          categories: state.categories.map(category =>
            category.id === id ? { ...category, ...updates } : category
          ),
        }));
      },

      deleteCategory: (id) => {
        set(state => ({
          categories: state.categories.filter(category => category.id !== id),
          todos: state.todos.map(todo =>
            todo.categoryId === id ? { ...todo, categoryId: undefined } : todo
          ),
        }));
      },

      // Tag actions
      addTag: (tagData) => {
        const newTag: Tag = {
          ...tagData,
          id: generateId(),
        };
        set(state => ({
          tags: [...state.tags, newTag],
        }));
      },

      updateTag: (id, updates) => {
        set(state => ({
          tags: state.tags.map(tag =>
            tag.id === id ? { ...tag, ...updates } : tag
          ),
        }));
      },

      deleteTag: (id) => {
        set(state => ({
          tags: state.tags.filter(tag => tag.id !== id),
          todos: state.todos.map(todo => ({
            ...todo,
            tags: todo.tags.filter(tagId => tagId !== id),
          })),
        }));
      },

      // Filter and sort actions
      setFilter: (newFilter) => {
        set(state => ({
          filter: { ...state.filter, ...newFilter },
        }));
      },

      clearFilter: () => {
        set({ filter: defaultFilter });
      },

      setSort: (sort) => {
        set({ sort });
      },

      // Settings actions
      updateSettings: (newSettings) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings },
        }));
      },

      // Selection actions
      selectTodos: (ids) => {
        set({ selectedTodos: ids });
      },

      toggleTodoSelection: (id) => {
        set(state => ({
          selectedTodos: state.selectedTodos.includes(id)
            ? state.selectedTodos.filter(selectedId => selectedId !== id)
            : [...state.selectedTodos, id],
        }));
      },

      clearSelection: () => {
        set({ selectedTodos: [] });
      },

      selectAll: () => {
        const todos = get().todos;
        set({ selectedTodos: todos.map(todo => todo.id) });
      },

      // Utility actions
      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      setError: (error) => {
        set({ error });
      },

      importData: (data) => {
        set({
          todos: data.todos || [],
          categories: data.categories || [],
          tags: data.tags || [],
        });
      },

      exportData: () => {
        const { todos, categories, tags } = get();
        return { todos, categories, tags };
      },

      resetStore: () => {
        set({
          todos: [],
          categories: [],
          tags: [],
          filter: defaultFilter,
          sort: defaultSort,
          selectedTodos: [],
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'todo-store',
      partialize: (state) => ({
        todos: state.todos,
        categories: state.categories,
        tags: state.tags,
        settings: state.settings,
      }),
    }
  )
);
