import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format, isToday, isTomorrow, isYesterday, isPast, isFuture } from 'date-fns';
import { Todo, Priority, TodoStatus } from '@/types/todo';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export function formatDate(date: Date): string {
  if (isToday(date)) {
    return `今天 ${format(date, 'HH:mm')}`;
  }
  if (isTomorrow(date)) {
    return `明天 ${format(date, 'HH:mm')}`;
  }
  if (isYesterday(date)) {
    return `昨天 ${format(date, 'HH:mm')}`;
  }
  return format(date, 'MM月dd日 HH:mm');
}

export function formatRelativeDate(date: Date): string {
  if (isToday(date)) {
    return '今天';
  }
  if (isTomorrow(date)) {
    return '明天';
  }
  if (isYesterday(date)) {
    return '昨天';
  }
  return format(date, 'MM月dd日');
}

export function getPriorityColor(priority: Priority): string {
  switch (priority) {
    case 'urgent':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'high':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low':
      return 'text-green-600 bg-green-50 border-green-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export function getPriorityLabel(priority: Priority): string {
  switch (priority) {
    case 'urgent':
      return '紧急';
    case 'high':
      return '高';
    case 'medium':
      return '中';
    case 'low':
      return '低';
    default:
      return '无';
  }
}

export function getStatusColor(status: TodoStatus): string {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'in-progress':
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'pending':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case 'cancelled':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export function getStatusLabel(status: TodoStatus): string {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'in-progress':
      return '进行中';
    case 'pending':
      return '待处理';
    case 'cancelled':
      return '已取消';
    default:
      return '未知';
  }
}

export function isDueSoon(dueDate: Date, hours: number = 24): boolean {
  const now = new Date();
  const timeDiff = dueDate.getTime() - now.getTime();
  const hoursDiff = timeDiff / (1000 * 60 * 60);
  return hoursDiff > 0 && hoursDiff <= hours;
}

export function isOverdue(dueDate: Date): boolean {
  return isPast(dueDate) && !isToday(dueDate);
}

export function sortTodos(todos: Todo[], sortField: string, sortDirection: 'asc' | 'desc'): Todo[] {
  return [...todos].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortField) {
      case 'title':
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      case 'createdAt':
        aValue = a.createdAt.getTime();
        bValue = b.createdAt.getTime();
        break;
      case 'updatedAt':
        aValue = a.updatedAt.getTime();
        bValue = b.updatedAt.getTime();
        break;
      case 'dueDate':
        aValue = a.dueDate?.getTime() || Infinity;
        bValue = b.dueDate?.getTime() || Infinity;
        break;
      case 'priority':
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        aValue = priorityOrder[a.priority];
        bValue = priorityOrder[b.priority];
        break;
      case 'order':
        aValue = a.order;
        bValue = b.order;
        break;
      default:
        return 0;
    }

    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });
}

export function filterTodos(todos: Todo[], filter: any): Todo[] {
  return todos.filter(todo => {
    // Status filter
    if (filter.status && filter.status.length > 0) {
      if (!filter.status.includes(todo.status)) {
        return false;
      }
    }

    // Priority filter
    if (filter.priority && filter.priority.length > 0) {
      if (!filter.priority.includes(todo.priority)) {
        return false;
      }
    }

    // Category filter
    if (filter.categoryId && todo.categoryId !== filter.categoryId) {
      return false;
    }

    // Tags filter
    if (filter.tagIds && filter.tagIds.length > 0) {
      const hasMatchingTag = filter.tagIds.some((tagId: string) => 
        todo.tags.includes(tagId)
      );
      if (!hasMatchingTag) {
        return false;
      }
    }

    // Search query filter
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      const matchesTitle = todo.title.toLowerCase().includes(query);
      const matchesDescription = todo.description?.toLowerCase().includes(query);
      if (!matchesTitle && !matchesDescription) {
        return false;
      }
    }

    // Due date range filter
    if (filter.dueDateRange) {
      if (filter.dueDateRange.start && todo.dueDate) {
        if (todo.dueDate < filter.dueDateRange.start) {
          return false;
        }
      }
      if (filter.dueDateRange.end && todo.dueDate) {
        if (todo.dueDate > filter.dueDateRange.end) {
          return false;
        }
      }
    }

    // Archived filter
    if (!filter.showArchived && todo.isArchived) {
      return false;
    }

    return true;
  });
}

export function getCompletionPercentage(todos: Todo[]): number {
  if (todos.length === 0) return 0;
  const completedCount = todos.filter(todo => todo.status === 'completed').length;
  return Math.round((completedCount / todos.length) * 100);
}

export function groupTodosByDate(todos: Todo[]): Record<string, Todo[]> {
  const groups: Record<string, Todo[]> = {};
  
  todos.forEach(todo => {
    if (todo.dueDate) {
      const dateKey = format(todo.dueDate, 'yyyy-MM-dd');
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(todo);
    } else {
      if (!groups['no-date']) {
        groups['no-date'] = [];
      }
      groups['no-date'].push(todo);
    }
  });

  return groups;
}

export function exportTodosToJSON(todos: Todo[]): string {
  return JSON.stringify(todos, null, 2);
}

export function importTodosFromJSON(jsonString: string): Todo[] {
  try {
    const parsed = JSON.parse(jsonString);
    return Array.isArray(parsed) ? parsed : [];
  } catch {
    return [];
  }
}
