'use client';

import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Calendar,
  Clock,
  Flag,
  MoreHorizontal,
  Edit,
  Trash2,
  Archive,
  Copy,
  CheckSquare,
  Square,
  GripVertical,
  MessageSquare,
  Paperclip,
} from 'lucide-react';
import { Todo, Category, Tag } from '@/types/todo';
import { useTodoStore } from '@/store/todoStore';
import { formatDate, getPriorityColor, getPriorityLabel, getStatusColor, isOverdue, isDueSoon } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface TodoItemProps {
  todo: Todo;
  categories: Category[];
  tags: Tag[];
  compact?: boolean;
}

export function TodoItem({ todo, categories, tags, compact = false }: TodoItemProps) {
  const [showActions, setShowActions] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const {
    toggleTodo,
    updateTodo,
    deleteTodo,
    duplicateTodo,
    archiveTodo,
    selectedTodos,
    toggleTodoSelection,
  } = useTodoStore();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: todo.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const category = categories.find(c => c.id === todo.categoryId);
  const todoTags = tags.filter(tag => todo.tags.includes(tag.id));
  const isSelected = selectedTodos.includes(todo.id);
  const isCompleted = todo.status === 'completed';
  const hasSubtasks = todo.subtasks.length > 0;
  const completedSubtasks = todo.subtasks.filter(st => st.completed).length;

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleTodo(todo.id);
  };

  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleTodoSelection(todo.id);
  };

  const handleAction = (action: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowActions(false);
    
    switch (action) {
      case 'edit':
        // TODO: Open edit modal
        break;
      case 'duplicate':
        duplicateTodo(todo.id);
        break;
      case 'archive':
        archiveTodo(todo.id);
        break;
      case 'delete':
        if (confirm('确定要删除这个任务吗？')) {
          deleteTodo(todo.id);
        }
        break;
    }
  };

  const priorityColor = getPriorityColor(todo.priority);
  const statusColor = getStatusColor(todo.status);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'group relative bg-white dark:bg-gray-800 transition-all duration-200',
        {
          'opacity-50': isDragging,
          'ring-2 ring-blue-500': isSelected,
          'opacity-60': isCompleted,
          'border-l-4': !compact,
        },
        compact ? 'p-4' : 'p-6',
        !compact && 'hover:bg-gray-50 dark:hover:bg-gray-700'
      )}
      style={{
        ...style,
        borderLeftColor: category?.color || '#e5e7eb',
      }}
      onClick={() => !compact && setShowDetails(!showDetails)}
    >
      {/* Drag Handle */}
      {!compact && (
        <div
          {...attributes}
          {...listeners}
          className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 cursor-grab active:cursor-grabbing"
        >
          <GripVertical className="w-4 h-4 text-gray-400" />
        </div>
      )}

      <div className={cn('flex items-start space-x-3', { 'ml-6': !compact })}>
        {/* Checkbox */}
        <button
          onClick={handleToggle}
          className={cn(
            'flex-shrink-0 mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors',
            isCompleted
              ? 'bg-green-500 border-green-500 text-white'
              : 'border-gray-300 dark:border-gray-600 hover:border-green-500'
          )}
        >
          {isCompleted ? (
            <CheckSquare className="w-3 h-3" />
          ) : (
            <Square className="w-3 h-3 opacity-0" />
          )}
        </button>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Title and Priority */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3
                className={cn(
                  'text-sm font-medium text-gray-900 dark:text-white',
                  { 'line-through text-gray-500 dark:text-gray-400': isCompleted }
                )}
              >
                {todo.title}
              </h3>
              {todo.description && (
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  {todo.description}
                </p>
              )}
            </div>

            {/* Priority Badge */}
            <span className={cn('ml-2 px-2 py-1 text-xs font-medium rounded-full border', priorityColor)}>
              {getPriorityLabel(todo.priority)}
            </span>
          </div>

          {/* Meta Information */}
          <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            {/* Category */}
            {category && (
              <div className="flex items-center space-x-1">
                <span>{category.icon || '📁'}</span>
                <span>{category.name}</span>
              </div>
            )}

            {/* Due Date */}
            {todo.dueDate && (
              <div
                className={cn('flex items-center space-x-1', {
                  'text-red-600 dark:text-red-400': isOverdue(todo.dueDate),
                  'text-yellow-600 dark:text-yellow-400': isDueSoon(todo.dueDate),
                })}
              >
                <Calendar className="w-3 h-3" />
                <span>{formatDate(todo.dueDate)}</span>
              </div>
            )}

            {/* Subtasks */}
            {hasSubtasks && (
              <div className="flex items-center space-x-1">
                <CheckSquare className="w-3 h-3" />
                <span>{completedSubtasks}/{todo.subtasks.length}</span>
              </div>
            )}

            {/* Attachments */}
            {todo.attachments.length > 0 && (
              <div className="flex items-center space-x-1">
                <Paperclip className="w-3 h-3" />
                <span>{todo.attachments.length}</span>
              </div>
            )}

            {/* Comments/Notes */}
            {todo.description && (
              <div className="flex items-center space-x-1">
                <MessageSquare className="w-3 h-3" />
              </div>
            )}
          </div>

          {/* Tags */}
          {todoTags.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {todoTags.map(tag => (
                <span
                  key={tag.id}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full text-white"
                  style={{ backgroundColor: tag.color }}
                >
                  {tag.name}
                </span>
              ))}
            </div>
          )}

          {/* Subtasks Preview */}
          {showDetails && hasSubtasks && (
            <div className="mt-3 space-y-1">
              {todo.subtasks.slice(0, 3).map(subtask => (
                <div key={subtask.id} className="flex items-center space-x-2 text-sm">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const updatedSubtasks = todo.subtasks.map(st =>
                        st.id === subtask.id ? { ...st, completed: !st.completed } : st
                      );
                      updateTodo(todo.id, { subtasks: updatedSubtasks });
                    }}
                    className={cn(
                      'w-4 h-4 rounded border flex items-center justify-center',
                      subtask.completed
                        ? 'bg-green-500 border-green-500 text-white'
                        : 'border-gray-300 dark:border-gray-600'
                    )}
                  >
                    {subtask.completed && <CheckSquare className="w-2 h-2" />}
                  </button>
                  <span
                    className={cn(
                      'text-gray-700 dark:text-gray-300',
                      { 'line-through text-gray-500 dark:text-gray-400': subtask.completed }
                    )}
                  >
                    {subtask.title}
                  </span>
                </div>
              ))}
              {todo.subtasks.length > 3 && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  还有 {todo.subtasks.length - 3} 个子任务...
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1">
          {/* Selection Checkbox */}
          <button
            onClick={handleSelect}
            className={cn(
              'w-4 h-4 rounded border flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity',
              isSelected
                ? 'bg-blue-500 border-blue-500 text-white opacity-100'
                : 'border-gray-300 dark:border-gray-600 hover:border-blue-500'
            )}
          >
            {isSelected && <CheckSquare className="w-2 h-2" />}
          </button>

          {/* More Actions */}
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowActions(!showActions);
              }}
              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>

            {showActions && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                <div className="py-1">
                  <button
                    onClick={(e) => handleAction('edit', e)}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                  >
                    <Edit className="w-4 h-4" />
                    <span>编辑</span>
                  </button>
                  <button
                    onClick={(e) => handleAction('duplicate', e)}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                  >
                    <Copy className="w-4 h-4" />
                    <span>复制</span>
                  </button>
                  <button
                    onClick={(e) => handleAction('archive', e)}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                  >
                    <Archive className="w-4 h-4" />
                    <span>归档</span>
                  </button>
                  <button
                    onClick={(e) => handleAction('delete', e)}
                    className="w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>删除</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
