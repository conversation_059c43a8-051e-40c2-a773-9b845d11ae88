'use client';

import { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Todo, Category, Tag } from '@/types/todo';
import { useTodoStore } from '@/store/todoStore';
import { TodoItem } from './TodoItem';
import { EmptyState } from './EmptyState';

interface TodoListProps {
  todos: Todo[];
  categories: Category[];
  tags: Tag[];
  view: 'list' | 'grid' | 'kanban';
}

export function TodoList({ todos, categories, tags, view }: TodoListProps) {
  const { reorderTodos } = useTodoStore();
  
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = todos.findIndex(todo => todo.id === active.id);
      const newIndex = todos.findIndex(todo => todo.id === over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        reorderTodos(oldIndex, newIndex);
      }
    }
  };

  if (todos.length === 0) {
    return <EmptyState />;
  }

  const renderListView = () => (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={todos.map(todo => todo.id)} strategy={verticalListSortingStrategy}>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {todos.map(todo => (
            <TodoItem
              key={todo.id}
              todo={todo}
              categories={categories}
              tags={tags}
            />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  );

  const renderGridView = () => (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={todos.map(todo => todo.id)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6">
          {todos.map(todo => (
            <div key={todo.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <TodoItem
                todo={todo}
                categories={categories}
                tags={tags}
                compact
              />
            </div>
          ))}
        </div>
      </SortableContext>
    </DndContext>
  );

  const renderKanbanView = () => {
    const statusColumns = [
      { key: 'pending', title: '待处理', todos: todos.filter(t => t.status === 'pending') },
      { key: 'in-progress', title: '进行中', todos: todos.filter(t => t.status === 'in-progress') },
      { key: 'completed', title: '已完成', todos: todos.filter(t => t.status === 'completed') },
    ];

    return (
      <div className="flex space-x-6 p-6 h-full overflow-x-auto">
        {statusColumns.map(column => (
          <div key={column.key} className="flex-shrink-0 w-80">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center justify-between">
                {column.title}
                <span className="text-sm text-gray-500 bg-white dark:bg-gray-600 px-2 py-1 rounded-full">
                  {column.todos.length}
                </span>
              </h3>
              <div className="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto">
                {column.todos.map(todo => (
                  <div key={todo.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                    <TodoItem
                      todo={todo}
                      categories={categories}
                      tags={tags}
                      compact
                    />
                  </div>
                ))}
                {column.todos.length === 0 && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    暂无任务
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full overflow-auto">
      {view === 'list' && renderListView()}
      {view === 'grid' && renderGridView()}
      {view === 'kanban' && renderKanbanView()}
    </div>
  );
}
