'use client';

import { useState, useEffect } from 'react';
import { useTodoStore } from '@/store/todoStore';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { TodoList } from './TodoList';
import { AddTodoModal } from './AddTodoModal';
import { SettingsModal } from './SettingsModal';
import { filterTodos, sortTodos } from '@/lib/utils';

export function TodoApp() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const {
    todos,
    categories,
    tags,
    filter,
    sort,
    settings,
    selectedTodos,
    clearSelection,
  } = useTodoStore();

  // Filter and sort todos
  const filteredTodos = filterTodos(todos, filter);
  const sortedTodos = sortTodos(filteredTodos, sort.field, sort.direction);

  // Apply theme
  useEffect(() => {
    const root = document.documentElement;
    if (settings.theme === 'dark') {
      root.classList.add('dark');
    } else if (settings.theme === 'light') {
      root.classList.remove('dark');
    } else {
      // System theme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      if (mediaQuery.matches) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }
  }, [settings.theme]);

  // Clear selection when clicking outside
  const handleBackdropClick = () => {
    if (selectedTodos.length > 0) {
      clearSelection();
    }
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Sidebar */}
      <div
        className={`${
          sidebarOpen ? 'w-80' : 'w-0'
        } transition-all duration-300 overflow-hidden border-r border-gray-200 dark:border-gray-700`}
      >
        <Sidebar
          categories={categories}
          tags={tags}
          onClose={() => setSidebarOpen(false)}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <Header
          onAddTodo={() => setShowAddModal(true)}
          onOpenSettings={() => setShowSettingsModal(true)}
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          sidebarOpen={sidebarOpen}
          selectedCount={selectedTodos.length}
        />

        {/* Todo List */}
        <main
          className="flex-1 overflow-hidden"
          onClick={handleBackdropClick}
        >
          <TodoList
            todos={sortedTodos}
            categories={categories}
            tags={tags}
            view={settings.defaultView}
          />
        </main>
      </div>

      {/* Modals */}
      {showAddModal && (
        <AddTodoModal
          onClose={() => setShowAddModal(false)}
          categories={categories}
          tags={tags}
        />
      )}

      {showSettingsModal && (
        <SettingsModal
          onClose={() => setShowSettingsModal(false)}
        />
      )}
    </div>
  );
}
