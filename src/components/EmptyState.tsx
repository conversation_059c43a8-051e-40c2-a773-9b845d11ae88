'use client';

import { CheckSquare, Plus } from 'lucide-react';

export function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center h-full py-12">
      <div className="text-center">
        <CheckSquare className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          暂无任务
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm">
          看起来您还没有任何任务。点击上方的"新任务"按钮来创建您的第一个任务吧！
        </p>
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-400 dark:text-gray-500">
          <Plus className="w-4 h-4" />
          <span>点击"新任务"开始</span>
        </div>
      </div>
    </div>
  );
}
