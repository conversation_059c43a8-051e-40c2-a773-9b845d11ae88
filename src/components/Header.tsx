'use client';

import { useState } from 'react';
import { 
  Plus, 
  Settings, 
  Search, 
  Filter,
  Menu,
  X,
  MoreHorizontal,
  Archive,
  Trash2,
  CheckSquare
} from 'lucide-react';
import { useTodoStore } from '@/store/todoStore';
import { SearchModal } from './SearchModal';
import { FilterModal } from './FilterModal';

interface HeaderProps {
  onAddTodo: () => void;
  onOpenSettings: () => void;
  onToggleSidebar: () => void;
  sidebarOpen: boolean;
  selectedCount: number;
}

export function Header({
  onAddTodo,
  onOpenSettings,
  onToggleSidebar,
  sidebarOpen,
  selectedCount,
}: HeaderProps) {
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  const {
    selectedTodos,
    bulkUpdateTodos,
    deleteTodo,
    clearSelection,
    filter,
  } = useTodoStore();

  const handleBulkComplete = () => {
    bulkUpdateTodos(selectedTodos, { status: 'completed', completedAt: new Date() });
    clearSelection();
    setShowBulkActions(false);
  };

  const handleBulkArchive = () => {
    bulkUpdateTodos(selectedTodos, { isArchived: true });
    clearSelection();
    setShowBulkActions(false);
  };

  const handleBulkDelete = () => {
    if (confirm(`确定要删除 ${selectedCount} 个任务吗？此操作无法撤销。`)) {
      selectedTodos.forEach(id => deleteTodo(id));
      clearSelection();
      setShowBulkActions(false);
    }
  };

  const hasActiveFilters = Object.keys(filter).some(key => {
    const value = filter[key as keyof typeof filter];
    if (key === 'showArchived') return false; // This is not considered an active filter
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'string') return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== undefined);
    }
    return false;
  });

  return (
    <>
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onToggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title={sidebarOpen ? '隐藏侧边栏' : '显示侧边栏'}
            >
              {sidebarOpen ? (
                <X className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              ) : (
                <Menu className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              )}
            </button>

            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                高级待办
              </h1>
              {selectedCount > 0 && (
                <span className="px-2 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                  已选择 {selectedCount} 项
                </span>
              )}
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-2">
            {selectedCount > 0 ? (
              // Bulk actions
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleBulkComplete}
                  className="p-2 rounded-lg hover:bg-green-100 dark:hover:bg-green-900 text-green-600 dark:text-green-400 transition-colors"
                  title="标记为完成"
                >
                  <CheckSquare className="w-5 h-5" />
                </button>
                <button
                  onClick={handleBulkArchive}
                  className="p-2 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900 text-yellow-600 dark:text-yellow-400 transition-colors"
                  title="归档"
                >
                  <Archive className="w-5 h-5" />
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="p-2 rounded-lg hover:bg-red-100 dark:hover:bg-red-900 text-red-600 dark:text-red-400 transition-colors"
                  title="删除"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
                <button
                  onClick={clearSelection}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
                  title="取消选择"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            ) : (
              // Normal actions
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowSearchModal(true)}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
                  title="搜索任务"
                >
                  <Search className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setShowFilterModal(true)}
                  className={`p-2 rounded-lg transition-colors ${
                    hasActiveFilters
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                  title="筛选任务"
                >
                  <Filter className="w-5 h-5" />
                </button>
                <button
                  onClick={onOpenSettings}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
                  title="设置"
                >
                  <Settings className="w-5 h-5" />
                </button>
                <button
                  onClick={onAddTodo}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  title="添加新任务"
                >
                  <Plus className="w-5 h-5" />
                  <span className="hidden sm:inline">新任务</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Modals */}
      {showSearchModal && (
        <SearchModal onClose={() => setShowSearchModal(false)} />
      )}

      {showFilterModal && (
        <FilterModal onClose={() => setShowFilterModal(false)} />
      )}
    </>
  );
}
