'use client';

import { useState } from 'react';
import {
  Calendar,
  CheckSquare,
  Clock,
  Star,
  Archive,
  Tag,
  Folder,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
} from 'lucide-react';
import { useTodoStore } from '@/store/todoStore';
import { Category, Tag as TagType } from '@/types/todo';
import { getCompletionPercentage } from '@/lib/utils';

interface SidebarProps {
  categories: Category[];
  tags: TagType[];
  onClose: () => void;
}

export function Sidebar({ categories, tags, onClose }: SidebarProps) {
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showTagForm, setShowTagForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [editingTag, setEditingTag] = useState<TagType | null>(null);

  const {
    todos,
    filter,
    setFilter,
    addCategory,
    updateCategory,
    deleteCategory,
    addTag,
    updateTag,
    deleteTag,
  } = useTodoStore();

  const todayTodos = todos.filter(todo => {
    if (!todo.dueDate) return false;
    const today = new Date();
    const dueDate = new Date(todo.dueDate);
    return dueDate.toDateString() === today.toDateString();
  });

  const upcomingTodos = todos.filter(todo => {
    if (!todo.dueDate) return false;
    const today = new Date();
    const dueDate = new Date(todo.dueDate);
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 && diffDays <= 7;
  });

  const completedTodos = todos.filter(todo => todo.status === 'completed');
  const archivedTodos = todos.filter(todo => todo.isArchived);

  const handleQuickFilter = (filterType: string, value?: any) => {
    switch (filterType) {
      case 'all':
        setFilter({});
        break;
      case 'today':
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        setFilter({
          dueDateRange: {
            start: today,
            end: tomorrow,
          },
        });
        break;
      case 'upcoming':
        const now = new Date();
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        setFilter({
          dueDateRange: {
            start: now,
            end: nextWeek,
          },
        });
        break;
      case 'completed':
        setFilter({ status: ['completed'] });
        break;
      case 'archived':
        setFilter({ showArchived: true });
        break;
      case 'category':
        setFilter({ categoryId: value });
        break;
      case 'tag':
        setFilter({ tagIds: [value] });
        break;
      case 'priority':
        setFilter({ priority: [value] });
        break;
    }
  };

  const handleCategorySubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const name = formData.get('name') as string;
    const color = formData.get('color') as string;
    const icon = formData.get('icon') as string;

    if (editingCategory) {
      updateCategory(editingCategory.id, { name, color, icon });
      setEditingCategory(null);
    } else {
      addCategory({ name, color, icon });
    }
    setShowCategoryForm(false);
  };

  const handleTagSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const name = formData.get('name') as string;
    const color = formData.get('color') as string;

    if (editingTag) {
      updateTag(editingTag.id, { name, color });
      setEditingTag(null);
    } else {
      addTag({ name, color });
    }
    setShowTagForm(false);
  };

  return (
    <div className="h-full bg-white dark:bg-gray-800 flex flex-col">
      {/* Quick Filters */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
          快速筛选
        </h2>
        <nav className="space-y-1">
          <button
            onClick={() => handleQuickFilter('all')}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
              Object.keys(filter).length === 0 || (Object.keys(filter).length === 1 && filter.showArchived === false)
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
          >
            <CheckSquare className="w-4 h-4" />
            <span>所有任务</span>
            <span className="ml-auto text-sm text-gray-500">
              {todos.filter(t => !t.isArchived).length}
            </span>
          </button>

          <button
            onClick={() => handleQuickFilter('today')}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors"
          >
            <Calendar className="w-4 h-4" />
            <span>今天</span>
            <span className="ml-auto text-sm text-gray-500">
              {todayTodos.length}
            </span>
          </button>

          <button
            onClick={() => handleQuickFilter('upcoming')}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors"
          >
            <Clock className="w-4 h-4" />
            <span>即将到期</span>
            <span className="ml-auto text-sm text-gray-500">
              {upcomingTodos.length}
            </span>
          </button>

          <button
            onClick={() => handleQuickFilter('completed')}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors"
          >
            <CheckSquare className="w-4 h-4" />
            <span>已完成</span>
            <span className="ml-auto text-sm text-gray-500">
              {completedTodos.length}
            </span>
          </button>

          <button
            onClick={() => handleQuickFilter('archived')}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors"
          >
            <Archive className="w-4 h-4" />
            <span>已归档</span>
            <span className="ml-auto text-sm text-gray-500">
              {archivedTodos.length}
            </span>
          </button>
        </nav>
      </div>

      {/* Categories */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white">
            分类
          </h2>
          <button
            onClick={() => setShowCategoryForm(true)}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        {showCategoryForm && (
          <form onSubmit={handleCategorySubmit} className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <input
              name="name"
              placeholder="分类名称"
              defaultValue={editingCategory?.name}
              className="w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              required
            />
            <input
              name="icon"
              placeholder="图标 (emoji)"
              defaultValue={editingCategory?.icon}
              className="w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
            <input
              name="color"
              type="color"
              defaultValue={editingCategory?.color || '#3b82f6'}
              className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"
            />
            <div className="flex space-x-2 mt-2">
              <button
                type="submit"
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                {editingCategory ? '更新' : '添加'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowCategoryForm(false);
                  setEditingCategory(null);
                }}
                className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-400 dark:hover:bg-gray-500"
              >
                取消
              </button>
            </div>
          </form>
        )}

        <nav className="space-y-1">
          {categories.map(category => {
            const categoryTodos = todos.filter(todo => todo.categoryId === category.id && !todo.isArchived);
            return (
              <div
                key={category.id}
                className="group flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <button
                  onClick={() => handleQuickFilter('category', category.id)}
                  className="flex-1 flex items-center space-x-3 text-left"
                >
                  <span className="text-lg">{category.icon || '📁'}</span>
                  <span className="text-gray-700 dark:text-gray-300">{category.name}</span>
                  <span className="ml-auto text-sm text-gray-500">
                    {categoryTodos.length}
                  </span>
                </button>
                <div className="opacity-0 group-hover:opacity-100 flex space-x-1">
                  <button
                    onClick={() => {
                      setEditingCategory(category);
                      setShowCategoryForm(true);
                    }}
                    className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-500 dark:text-gray-400"
                  >
                    <Edit className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('确定要删除这个分类吗？')) {
                        deleteCategory(category.id);
                      }
                    }}
                    className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-red-500"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            );
          })}
        </nav>
      </div>

      {/* Tags */}
      <div className="p-4 flex-1 overflow-y-auto">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white">
            标签
          </h2>
          <button
            onClick={() => setShowTagForm(true)}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        {showTagForm && (
          <form onSubmit={handleTagSubmit} className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <input
              name="name"
              placeholder="标签名称"
              defaultValue={editingTag?.name}
              className="w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              required
            />
            <input
              name="color"
              type="color"
              defaultValue={editingTag?.color || '#3b82f6'}
              className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"
            />
            <div className="flex space-x-2 mt-2">
              <button
                type="submit"
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                {editingTag ? '更新' : '添加'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowTagForm(false);
                  setEditingTag(null);
                }}
                className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-400 dark:hover:bg-gray-500"
              >
                取消
              </button>
            </div>
          </form>
        )}

        <div className="space-y-1">
          {tags.map(tag => {
            const tagTodos = todos.filter(todo => todo.tags.includes(tag.id) && !todo.isArchived);
            return (
              <div
                key={tag.id}
                className="group flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <button
                  onClick={() => handleQuickFilter('tag', tag.id)}
                  className="flex-1 flex items-center space-x-3 text-left"
                >
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: tag.color }}
                  />
                  <span className="text-gray-700 dark:text-gray-300">{tag.name}</span>
                  <span className="ml-auto text-sm text-gray-500">
                    {tagTodos.length}
                  </span>
                </button>
                <div className="opacity-0 group-hover:opacity-100 flex space-x-1">
                  <button
                    onClick={() => {
                      setEditingTag(tag);
                      setShowTagForm(true);
                    }}
                    className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-500 dark:text-gray-400"
                  >
                    <Edit className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('确定要删除这个标签吗？')) {
                        deleteTag(tag.id);
                      }
                    }}
                    className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-red-500"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Progress */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          总体进度
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${getCompletionPercentage(todos.filter(t => !t.isArchived))}%` }}
          />
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {getCompletionPercentage(todos.filter(t => !t.isArchived))}% 完成
        </div>
      </div>
    </div>
  );
}
