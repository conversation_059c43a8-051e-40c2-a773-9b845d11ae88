'use client';

import { useState } from 'react';
import { X, Moon, Sun, Monitor } from 'lucide-react';
import { useTodoStore } from '@/store/todoStore';

interface SettingsModalProps {
  onClose: () => void;
}

export function SettingsModal({ onClose }: SettingsModalProps) {
  const { settings, updateSettings } = useTodoStore();
  const [localSettings, setLocalSettings] = useState(settings);

  const handleSave = () => {
    updateSettings(localSettings);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            设置
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4 space-y-6">
          {/* Theme */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              主题
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'light', label: '浅色', icon: Sun },
                { value: 'dark', label: '深色', icon: Moon },
                { value: 'system', label: '系统', icon: Monitor },
              ].map(theme => {
                const Icon = theme.icon;
                return (
                  <button
                    key={theme.value}
                    onClick={() => setLocalSettings({ ...localSettings, theme: theme.value as any })}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      localSettings.theme === theme.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                  >
                    <Icon className="w-5 h-5 mx-auto mb-1 text-gray-600 dark:text-gray-400" />
                    <div className="text-xs text-gray-700 dark:text-gray-300">
                      {theme.label}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Default View */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              默认视图
            </h3>
            <select
              value={localSettings.defaultView}
              onChange={(e) => setLocalSettings({ ...localSettings, defaultView: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="list">列表视图</option>
              <option value="grid">网格视图</option>
              <option value="kanban">看板视图</option>
            </select>
          </div>

          {/* Default Priority */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              默认优先级
            </h3>
            <select
              value={localSettings.defaultPriority}
              onChange={(e) => setLocalSettings({ ...localSettings, defaultPriority: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>

          {/* Options */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              选项
            </h3>
            
            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                显示已完成的任务
              </span>
              <input
                type="checkbox"
                checked={localSettings.showCompletedTasks}
                onChange={(e) => setLocalSettings({ ...localSettings, showCompletedTasks: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>

            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                启用通知
              </span>
              <input
                type="checkbox"
                checked={localSettings.enableNotifications}
                onChange={(e) => setLocalSettings({ ...localSettings, enableNotifications: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>

            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                自动归档已完成任务
              </span>
              <input
                type="checkbox"
                checked={localSettings.autoArchiveCompleted}
                onChange={(e) => setLocalSettings({ ...localSettings, autoArchiveCompleted: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>
          </div>

          {/* Auto Archive Days */}
          {localSettings.autoArchiveCompleted && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                自动归档天数
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={localSettings.autoArchiveDays}
                onChange={(e) => setLocalSettings({ ...localSettings, autoArchiveDays: parseInt(e.target.value) || 30 })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          {/* Working Hours */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              工作时间
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                  开始时间
                </label>
                <input
                  type="time"
                  value={localSettings.workingHours.start}
                  onChange={(e) => setLocalSettings({
                    ...localSettings,
                    workingHours: { ...localSettings.workingHours, start: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                  结束时间
                </label>
                <input
                  type="time"
                  value={localSettings.workingHours.end}
                  onChange={(e) => setLocalSettings({
                    ...localSettings,
                    workingHours: { ...localSettings.workingHours, end: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2 p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            保存设置
          </button>
        </div>
      </div>
    </div>
  );
}
