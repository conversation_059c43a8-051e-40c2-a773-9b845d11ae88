[{"name": "hot-reloader", "duration": 98, "timestamp": 1834212990561, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751554264017, "traceId": "f216e397ba558857"}, {"name": "setup-dev-bundler", "duration": 463529, "timestamp": 1834212852905, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751554263879, "traceId": "f216e397ba558857"}, {"name": "run-instrumentation-hook", "duration": 14, "timestamp": 1834213346887, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751554264373, "traceId": "f216e397ba558857"}, {"name": "start-dev-server", "duration": 800721, "timestamp": 1834212557653, "id": 1, "tags": {"cpus": "8", "platform": "darwin", "memory.freeMem": "136200192", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8640266240", "memory.rss": "273743872", "memory.heapTotal": "103972864", "memory.heapUsed": "66349032"}, "startTime": 1751554263584, "traceId": "f216e397ba558857"}, {"name": "compile-path", "duration": 2044539, "timestamp": 1834235676077, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751554286701, "traceId": "f216e397ba558857"}, {"name": "ensure-page", "duration": 2045339, "timestamp": 1834235675654, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751554286700, "traceId": "f216e397ba558857"}]