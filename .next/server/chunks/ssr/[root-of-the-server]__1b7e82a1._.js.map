{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, isToday, isTomorrow, isYesterday, isPast, isFuture } from 'date-fns';\nimport { Todo, Priority, TodoStatus } from '@/types/todo';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n\nexport function formatDate(date: Date): string {\n  if (isToday(date)) {\n    return `今天 ${format(date, 'HH:mm')}`;\n  }\n  if (isTomorrow(date)) {\n    return `明天 ${format(date, 'HH:mm')}`;\n  }\n  if (isYesterday(date)) {\n    return `昨天 ${format(date, 'HH:mm')}`;\n  }\n  return format(date, 'MM月dd日 HH:mm');\n}\n\nexport function formatRelativeDate(date: Date): string {\n  if (isToday(date)) {\n    return '今天';\n  }\n  if (isTomorrow(date)) {\n    return '明天';\n  }\n  if (isYesterday(date)) {\n    return '昨天';\n  }\n  return format(date, 'MM月dd日');\n}\n\nexport function getPriorityColor(priority: Priority): string {\n  switch (priority) {\n    case 'urgent':\n      return 'text-red-600 bg-red-50 border-red-200';\n    case 'high':\n      return 'text-orange-600 bg-orange-50 border-orange-200';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    case 'low':\n      return 'text-green-600 bg-green-50 border-green-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n}\n\nexport function getPriorityLabel(priority: Priority): string {\n  switch (priority) {\n    case 'urgent':\n      return '紧急';\n    case 'high':\n      return '高';\n    case 'medium':\n      return '中';\n    case 'low':\n      return '低';\n    default:\n      return '无';\n  }\n}\n\nexport function getStatusColor(status: TodoStatus): string {\n  switch (status) {\n    case 'completed':\n      return 'text-green-600 bg-green-50 border-green-200';\n    case 'in-progress':\n      return 'text-blue-600 bg-blue-50 border-blue-200';\n    case 'pending':\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n    case 'cancelled':\n      return 'text-red-600 bg-red-50 border-red-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n}\n\nexport function getStatusLabel(status: TodoStatus): string {\n  switch (status) {\n    case 'completed':\n      return '已完成';\n    case 'in-progress':\n      return '进行中';\n    case 'pending':\n      return '待处理';\n    case 'cancelled':\n      return '已取消';\n    default:\n      return '未知';\n  }\n}\n\nexport function isDueSoon(dueDate: Date, hours: number = 24): boolean {\n  const now = new Date();\n  const timeDiff = dueDate.getTime() - now.getTime();\n  const hoursDiff = timeDiff / (1000 * 60 * 60);\n  return hoursDiff > 0 && hoursDiff <= hours;\n}\n\nexport function isOverdue(dueDate: Date): boolean {\n  return isPast(dueDate) && !isToday(dueDate);\n}\n\nexport function sortTodos(todos: Todo[], sortField: string, sortDirection: 'asc' | 'desc'): Todo[] {\n  return [...todos].sort((a, b) => {\n    let aValue: any;\n    let bValue: any;\n\n    switch (sortField) {\n      case 'title':\n        aValue = a.title.toLowerCase();\n        bValue = b.title.toLowerCase();\n        break;\n      case 'createdAt':\n        aValue = a.createdAt.getTime();\n        bValue = b.createdAt.getTime();\n        break;\n      case 'updatedAt':\n        aValue = a.updatedAt.getTime();\n        bValue = b.updatedAt.getTime();\n        break;\n      case 'dueDate':\n        aValue = a.dueDate?.getTime() || Infinity;\n        bValue = b.dueDate?.getTime() || Infinity;\n        break;\n      case 'priority':\n        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };\n        aValue = priorityOrder[a.priority];\n        bValue = priorityOrder[b.priority];\n        break;\n      case 'order':\n        aValue = a.order;\n        bValue = b.order;\n        break;\n      default:\n        return 0;\n    }\n\n    if (aValue < bValue) {\n      return sortDirection === 'asc' ? -1 : 1;\n    }\n    if (aValue > bValue) {\n      return sortDirection === 'asc' ? 1 : -1;\n    }\n    return 0;\n  });\n}\n\nexport function filterTodos(todos: Todo[], filter: any): Todo[] {\n  return todos.filter(todo => {\n    // Status filter\n    if (filter.status && filter.status.length > 0) {\n      if (!filter.status.includes(todo.status)) {\n        return false;\n      }\n    }\n\n    // Priority filter\n    if (filter.priority && filter.priority.length > 0) {\n      if (!filter.priority.includes(todo.priority)) {\n        return false;\n      }\n    }\n\n    // Category filter\n    if (filter.categoryId && todo.categoryId !== filter.categoryId) {\n      return false;\n    }\n\n    // Tags filter\n    if (filter.tagIds && filter.tagIds.length > 0) {\n      const hasMatchingTag = filter.tagIds.some((tagId: string) => \n        todo.tags.includes(tagId)\n      );\n      if (!hasMatchingTag) {\n        return false;\n      }\n    }\n\n    // Search query filter\n    if (filter.searchQuery) {\n      const query = filter.searchQuery.toLowerCase();\n      const matchesTitle = todo.title.toLowerCase().includes(query);\n      const matchesDescription = todo.description?.toLowerCase().includes(query);\n      if (!matchesTitle && !matchesDescription) {\n        return false;\n      }\n    }\n\n    // Due date range filter\n    if (filter.dueDateRange) {\n      if (filter.dueDateRange.start && todo.dueDate) {\n        if (todo.dueDate < filter.dueDateRange.start) {\n          return false;\n        }\n      }\n      if (filter.dueDateRange.end && todo.dueDate) {\n        if (todo.dueDate > filter.dueDateRange.end) {\n          return false;\n        }\n      }\n    }\n\n    // Archived filter\n    if (!filter.showArchived && todo.isArchived) {\n      return false;\n    }\n\n    return true;\n  });\n}\n\nexport function getCompletionPercentage(todos: Todo[]): number {\n  if (todos.length === 0) return 0;\n  const completedCount = todos.filter(todo => todo.status === 'completed').length;\n  return Math.round((completedCount / todos.length) * 100);\n}\n\nexport function groupTodosByDate(todos: Todo[]): Record<string, Todo[]> {\n  const groups: Record<string, Todo[]> = {};\n  \n  todos.forEach(todo => {\n    if (todo.dueDate) {\n      const dateKey = format(todo.dueDate, 'yyyy-MM-dd');\n      if (!groups[dateKey]) {\n        groups[dateKey] = [];\n      }\n      groups[dateKey].push(todo);\n    } else {\n      if (!groups['no-date']) {\n        groups['no-date'] = [];\n      }\n      groups['no-date'].push(todo);\n    }\n  });\n\n  return groups;\n}\n\nexport function exportTodosToJSON(todos: Todo[]): string {\n  return JSON.stringify(todos, null, 2);\n}\n\nexport function importTodosFromJSON(jsonString: string): Todo[] {\n  try {\n    const parsed = JSON.parse(jsonString);\n    return Array.isArray(parsed) ? parsed : [];\n  } catch {\n    return [];\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,WAAW,IAAU;IACnC,IAAI,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACjB,OAAO,CAAC,GAAG,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IACtC;IACA,IAAI,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE,OAAO;QACpB,OAAO,CAAC,GAAG,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IACtC;IACA,IAAI,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrB,OAAO,CAAC,GAAG,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IACtC;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;AACtB;AAEO,SAAS,mBAAmB,IAAU;IAC3C,IAAI,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACjB,OAAO;IACT;IACA,IAAI,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE,OAAO;QACpB,OAAO;IACT;IACA,IAAI,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrB,OAAO;IACT;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;AACtB;AAEO,SAAS,iBAAiB,QAAkB;IACjD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAkB;IACjD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,MAAkB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,MAAkB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,UAAU,OAAa,EAAE,QAAgB,EAAE;IACzD,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,QAAQ,OAAO,KAAK,IAAI,OAAO;IAChD,MAAM,YAAY,WAAW,CAAC,OAAO,KAAK,EAAE;IAC5C,OAAO,YAAY,KAAK,aAAa;AACvC;AAEO,SAAS,UAAU,OAAa;IACrC,OAAO,CAAA,GAAA,qIAAA,CAAA,SAAM,AAAD,EAAE,YAAY,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE;AACrC;AAEO,SAAS,UAAU,KAAa,EAAE,SAAiB,EAAE,aAA6B;IACvF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,IAAI;QACJ,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,SAAS,EAAE,KAAK,CAAC,WAAW;gBAC5B,SAAS,EAAE,KAAK,CAAC,WAAW;gBAC5B;YACF,KAAK;gBACH,SAAS,EAAE,SAAS,CAAC,OAAO;gBAC5B,SAAS,EAAE,SAAS,CAAC,OAAO;gBAC5B;YACF,KAAK;gBACH,SAAS,EAAE,SAAS,CAAC,OAAO;gBAC5B,SAAS,EAAE,SAAS,CAAC,OAAO;gBAC5B;YACF,KAAK;gBACH,SAAS,EAAE,OAAO,EAAE,aAAa;gBACjC,SAAS,EAAE,OAAO,EAAE,aAAa;gBACjC;YACF,KAAK;gBACH,MAAM,gBAAgB;oBAAE,QAAQ;oBAAG,MAAM;oBAAG,QAAQ;oBAAG,KAAK;gBAAE;gBAC9D,SAAS,aAAa,CAAC,EAAE,QAAQ,CAAC;gBAClC,SAAS,aAAa,CAAC,EAAE,QAAQ,CAAC;gBAClC;YACF,KAAK;gBACH,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB;YACF;gBACE,OAAO;QACX;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,kBAAkB,QAAQ,CAAC,IAAI;QACxC;QACA,IAAI,SAAS,QAAQ;YACnB,OAAO,kBAAkB,QAAQ,IAAI,CAAC;QACxC;QACA,OAAO;IACT;AACF;AAEO,SAAS,YAAY,KAAa,EAAE,MAAW;IACpD,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,gBAAgB;QAChB,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;YAC7C,IAAI,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,MAAM,GAAG;gBACxC,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;YACjD,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,QAAQ,GAAG;gBAC5C,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,OAAO,UAAU,IAAI,KAAK,UAAU,KAAK,OAAO,UAAU,EAAE;YAC9D,OAAO;QACT;QAEA,cAAc;QACd,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,iBAAiB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,QACzC,KAAK,IAAI,CAAC,QAAQ,CAAC;YAErB,IAAI,CAAC,gBAAgB;gBACnB,OAAO;YACT;QACF;QAEA,sBAAsB;QACtB,IAAI,OAAO,WAAW,EAAE;YACtB,MAAM,QAAQ,OAAO,WAAW,CAAC,WAAW;YAC5C,MAAM,eAAe,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YACvD,MAAM,qBAAqB,KAAK,WAAW,EAAE,cAAc,SAAS;YACpE,IAAI,CAAC,gBAAgB,CAAC,oBAAoB;gBACxC,OAAO;YACT;QACF;QAEA,wBAAwB;QACxB,IAAI,OAAO,YAAY,EAAE;YACvB,IAAI,OAAO,YAAY,CAAC,KAAK,IAAI,KAAK,OAAO,EAAE;gBAC7C,IAAI,KAAK,OAAO,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE;oBAC5C,OAAO;gBACT;YACF;YACA,IAAI,OAAO,YAAY,CAAC,GAAG,IAAI,KAAK,OAAO,EAAE;gBAC3C,IAAI,KAAK,OAAO,GAAG,OAAO,YAAY,CAAC,GAAG,EAAE;oBAC1C,OAAO;gBACT;YACF;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,OAAO,YAAY,IAAI,KAAK,UAAU,EAAE;YAC3C,OAAO;QACT;QAEA,OAAO;IACT;AACF;AAEO,SAAS,wBAAwB,KAAa;IACnD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAC/B,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;IAC/E,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,MAAM,GAAI;AACtD;AAEO,SAAS,iBAAiB,KAAa;IAC5C,MAAM,SAAiC,CAAC;IAExC,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,OAAO,EAAE;YAChB,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,OAAO,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACpB,MAAM,CAAC,QAAQ,GAAG,EAAE;YACtB;YACA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvB,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;gBACtB,MAAM,CAAC,UAAU,GAAG,EAAE;YACxB;YACA,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;QACzB;IACF;IAEA,OAAO;AACT;AAEO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,KAAK,SAAS,CAAC,OAAO,MAAM;AACrC;AAEO,SAAS,oBAAoB,UAAkB;IACpD,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;IAC5C,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/store/todoStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { Todo, Category, Tag, TodoFilter, TodoSort, AppSettings, Priority, TodoStatus } from '@/types/todo';\nimport { generateId } from '@/lib/utils';\n\ninterface TodoStore {\n  // State\n  todos: Todo[];\n  categories: Category[];\n  tags: Tag[];\n  filter: TodoFilter;\n  sort: TodoSort;\n  settings: AppSettings;\n  selectedTodos: string[];\n  isLoading: boolean;\n  error: string | null;\n\n  // Todo actions\n  addTodo: (todo: Omit<Todo, 'id' | 'createdAt' | 'updatedAt' | 'order'>) => void;\n  updateTodo: (id: string, updates: Partial<Todo>) => void;\n  deleteTodo: (id: string) => void;\n  toggleTodo: (id: string) => void;\n  reorderTodos: (sourceIndex: number, destinationIndex: number) => void;\n  bulkUpdateTodos: (ids: string[], updates: Partial<Todo>) => void;\n  duplicateTodo: (id: string) => void;\n  archiveTodo: (id: string) => void;\n  unarchiveTodo: (id: string) => void;\n\n  // Category actions\n  addCategory: (category: Omit<Category, 'id'>) => void;\n  updateCategory: (id: string, updates: Partial<Category>) => void;\n  deleteCategory: (id: string) => void;\n\n  // Tag actions\n  addTag: (tag: Omit<Tag, 'id'>) => void;\n  updateTag: (id: string, updates: Partial<Tag>) => void;\n  deleteTag: (id: string) => void;\n\n  // Filter and sort actions\n  setFilter: (filter: Partial<TodoFilter>) => void;\n  clearFilter: () => void;\n  setSort: (sort: TodoSort) => void;\n\n  // Settings actions\n  updateSettings: (settings: Partial<AppSettings>) => void;\n\n  // Selection actions\n  selectTodos: (ids: string[]) => void;\n  toggleTodoSelection: (id: string) => void;\n  clearSelection: () => void;\n  selectAll: () => void;\n\n  // Utility actions\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  importData: (data: { todos: Todo[]; categories: Category[]; tags: Tag[] }) => void;\n  exportData: () => { todos: Todo[]; categories: Category[]; tags: Tag[] };\n  resetStore: () => void;\n}\n\nconst defaultSettings: AppSettings = {\n  theme: 'system',\n  defaultView: 'list',\n  showCompletedTasks: true,\n  enableNotifications: true,\n  autoArchiveCompleted: false,\n  autoArchiveDays: 30,\n  defaultPriority: 'medium',\n  workingHours: {\n    start: '09:00',\n    end: '18:00',\n  },\n};\n\nconst defaultFilter: TodoFilter = {\n  showArchived: false,\n};\n\nconst defaultSort: TodoSort = {\n  field: 'order',\n  direction: 'asc',\n};\n\nexport const useTodoStore = create<TodoStore>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      todos: [],\n      categories: [\n        { id: 'work', name: '工作', color: '#3b82f6', icon: '💼' },\n        { id: 'personal', name: '个人', color: '#10b981', icon: '🏠' },\n        { id: 'shopping', name: '购物', color: '#f59e0b', icon: '🛒' },\n        { id: 'health', name: '健康', color: '#ef4444', icon: '❤️' },\n      ],\n      tags: [\n        { id: 'urgent', name: '紧急', color: '#ef4444' },\n        { id: 'important', name: '重要', color: '#f59e0b' },\n        { id: 'meeting', name: '会议', color: '#3b82f6' },\n        { id: 'review', name: '复习', color: '#8b5cf6' },\n      ],\n      filter: defaultFilter,\n      sort: defaultSort,\n      settings: defaultSettings,\n      selectedTodos: [],\n      isLoading: false,\n      error: null,\n\n      // Todo actions\n      addTodo: (todoData) => {\n        const now = new Date();\n        const todos = get().todos;\n        const maxOrder = Math.max(...todos.map(t => t.order), 0);\n        \n        const newTodo: Todo = {\n          ...todoData,\n          id: generateId(),\n          createdAt: now,\n          updatedAt: now,\n          order: maxOrder + 1,\n          subtasks: todoData.subtasks || [],\n          attachments: todoData.attachments || [],\n          reminders: todoData.reminders || [],\n        };\n\n        set(state => ({\n          todos: [...state.todos, newTodo],\n        }));\n      },\n\n      updateTodo: (id, updates) => {\n        set(state => ({\n          todos: state.todos.map(todo =>\n            todo.id === id\n              ? { ...todo, ...updates, updatedAt: new Date() }\n              : todo\n          ),\n        }));\n      },\n\n      deleteTodo: (id) => {\n        set(state => ({\n          todos: state.todos.filter(todo => todo.id !== id),\n          selectedTodos: state.selectedTodos.filter(selectedId => selectedId !== id),\n        }));\n      },\n\n      toggleTodo: (id) => {\n        set(state => ({\n          todos: state.todos.map(todo => {\n            if (todo.id === id) {\n              const newStatus: TodoStatus = todo.status === 'completed' ? 'pending' : 'completed';\n              return {\n                ...todo,\n                status: newStatus,\n                completedAt: newStatus === 'completed' ? new Date() : undefined,\n                updatedAt: new Date(),\n              };\n            }\n            return todo;\n          }),\n        }));\n      },\n\n      reorderTodos: (sourceIndex, destinationIndex) => {\n        set(state => {\n          const todos = [...state.todos];\n          const [removed] = todos.splice(sourceIndex, 1);\n          todos.splice(destinationIndex, 0, removed);\n          \n          // Update order values\n          const updatedTodos = todos.map((todo, index) => ({\n            ...todo,\n            order: index,\n            updatedAt: new Date(),\n          }));\n\n          return { todos: updatedTodos };\n        });\n      },\n\n      bulkUpdateTodos: (ids, updates) => {\n        set(state => ({\n          todos: state.todos.map(todo =>\n            ids.includes(todo.id)\n              ? { ...todo, ...updates, updatedAt: new Date() }\n              : todo\n          ),\n        }));\n      },\n\n      duplicateTodo: (id) => {\n        const todo = get().todos.find(t => t.id === id);\n        if (todo) {\n          const { id: _, createdAt: __, updatedAt: ___, completedAt: ____, ...todoData } = todo;\n          get().addTodo({\n            ...todoData,\n            title: `${todo.title} (副本)`,\n            status: 'pending',\n          });\n        }\n      },\n\n      archiveTodo: (id) => {\n        get().updateTodo(id, { isArchived: true });\n      },\n\n      unarchiveTodo: (id) => {\n        get().updateTodo(id, { isArchived: false });\n      },\n\n      // Category actions\n      addCategory: (categoryData) => {\n        const newCategory: Category = {\n          ...categoryData,\n          id: generateId(),\n        };\n        set(state => ({\n          categories: [...state.categories, newCategory],\n        }));\n      },\n\n      updateCategory: (id, updates) => {\n        set(state => ({\n          categories: state.categories.map(category =>\n            category.id === id ? { ...category, ...updates } : category\n          ),\n        }));\n      },\n\n      deleteCategory: (id) => {\n        set(state => ({\n          categories: state.categories.filter(category => category.id !== id),\n          todos: state.todos.map(todo =>\n            todo.categoryId === id ? { ...todo, categoryId: undefined } : todo\n          ),\n        }));\n      },\n\n      // Tag actions\n      addTag: (tagData) => {\n        const newTag: Tag = {\n          ...tagData,\n          id: generateId(),\n        };\n        set(state => ({\n          tags: [...state.tags, newTag],\n        }));\n      },\n\n      updateTag: (id, updates) => {\n        set(state => ({\n          tags: state.tags.map(tag =>\n            tag.id === id ? { ...tag, ...updates } : tag\n          ),\n        }));\n      },\n\n      deleteTag: (id) => {\n        set(state => ({\n          tags: state.tags.filter(tag => tag.id !== id),\n          todos: state.todos.map(todo => ({\n            ...todo,\n            tags: todo.tags.filter(tagId => tagId !== id),\n          })),\n        }));\n      },\n\n      // Filter and sort actions\n      setFilter: (newFilter) => {\n        set(state => ({\n          filter: { ...state.filter, ...newFilter },\n        }));\n      },\n\n      clearFilter: () => {\n        set({ filter: defaultFilter });\n      },\n\n      setSort: (sort) => {\n        set({ sort });\n      },\n\n      // Settings actions\n      updateSettings: (newSettings) => {\n        set(state => ({\n          settings: { ...state.settings, ...newSettings },\n        }));\n      },\n\n      // Selection actions\n      selectTodos: (ids) => {\n        set({ selectedTodos: ids });\n      },\n\n      toggleTodoSelection: (id) => {\n        set(state => ({\n          selectedTodos: state.selectedTodos.includes(id)\n            ? state.selectedTodos.filter(selectedId => selectedId !== id)\n            : [...state.selectedTodos, id],\n        }));\n      },\n\n      clearSelection: () => {\n        set({ selectedTodos: [] });\n      },\n\n      selectAll: () => {\n        const todos = get().todos;\n        set({ selectedTodos: todos.map(todo => todo.id) });\n      },\n\n      // Utility actions\n      setLoading: (loading) => {\n        set({ isLoading: loading });\n      },\n\n      setError: (error) => {\n        set({ error });\n      },\n\n      importData: (data) => {\n        set({\n          todos: data.todos || [],\n          categories: data.categories || [],\n          tags: data.tags || [],\n        });\n      },\n\n      exportData: () => {\n        const { todos, categories, tags } = get();\n        return { todos, categories, tags };\n      },\n\n      resetStore: () => {\n        set({\n          todos: [],\n          categories: [],\n          tags: [],\n          filter: defaultFilter,\n          sort: defaultSort,\n          selectedTodos: [],\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'todo-store',\n      partialize: (state) => ({\n        todos: state.todos,\n        categories: state.categories,\n        tags: state.tags,\n        settings: state.settings,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAyDA,MAAM,kBAA+B;IACnC,OAAO;IACP,aAAa;IACb,oBAAoB;IACpB,qBAAqB;IACrB,sBAAsB;IACtB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;QACZ,OAAO;QACP,KAAK;IACP;AACF;AAEA,MAAM,gBAA4B;IAChC,cAAc;AAChB;AAEA,MAAM,cAAwB;IAC5B,OAAO;IACP,WAAW;AACb;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,OAAO,EAAE;QACT,YAAY;YACV;gBAAE,IAAI;gBAAQ,MAAM;gBAAM,OAAO;gBAAW,MAAM;YAAK;YACvD;gBAAE,IAAI;gBAAY,MAAM;gBAAM,OAAO;gBAAW,MAAM;YAAK;YAC3D;gBAAE,IAAI;gBAAY,MAAM;gBAAM,OAAO;gBAAW,MAAM;YAAK;YAC3D;gBAAE,IAAI;gBAAU,MAAM;gBAAM,OAAO;gBAAW,MAAM;YAAK;SAC1D;QACD,MAAM;YACJ;gBAAE,IAAI;gBAAU,MAAM;gBAAM,OAAO;YAAU;YAC7C;gBAAE,IAAI;gBAAa,MAAM;gBAAM,OAAO;YAAU;YAChD;gBAAE,IAAI;gBAAW,MAAM;gBAAM,OAAO;YAAU;YAC9C;gBAAE,IAAI;gBAAU,MAAM;gBAAM,OAAO;YAAU;SAC9C;QACD,QAAQ;QACR,MAAM;QACN,UAAU;QACV,eAAe,EAAE;QACjB,WAAW;QACX,OAAO;QAEP,eAAe;QACf,SAAS,CAAC;YACR,MAAM,MAAM,IAAI;YAChB,MAAM,QAAQ,MAAM,KAAK;YACzB,MAAM,WAAW,KAAK,GAAG,IAAI,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG;YAEtD,MAAM,UAAgB;gBACpB,GAAG,QAAQ;gBACX,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,WAAW;gBACX,WAAW;gBACX,OAAO,WAAW;gBAClB,UAAU,SAAS,QAAQ,IAAI,EAAE;gBACjC,aAAa,SAAS,WAAW,IAAI,EAAE;gBACvC,WAAW,SAAS,SAAS,IAAI,EAAE;YACrC;YAEA,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO;2BAAI,MAAM,KAAK;wBAAE;qBAAQ;gBAClC,CAAC;QACH;QAEA,YAAY,CAAC,IAAI;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,KACR;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAC7C;gBAER,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;oBAC9C,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,aAAc,eAAe;gBACzE,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;wBACrB,IAAI,KAAK,EAAE,KAAK,IAAI;4BAClB,MAAM,YAAwB,KAAK,MAAM,KAAK,cAAc,YAAY;4BACxE,OAAO;gCACL,GAAG,IAAI;gCACP,QAAQ;gCACR,aAAa,cAAc,cAAc,IAAI,SAAS;gCACtD,WAAW,IAAI;4BACjB;wBACF;wBACA,OAAO;oBACT;gBACF,CAAC;QACH;QAEA,cAAc,CAAC,aAAa;YAC1B,IAAI,CAAA;gBACF,MAAM,QAAQ;uBAAI,MAAM,KAAK;iBAAC;gBAC9B,MAAM,CAAC,QAAQ,GAAG,MAAM,MAAM,CAAC,aAAa;gBAC5C,MAAM,MAAM,CAAC,kBAAkB,GAAG;gBAElC,sBAAsB;gBACtB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;wBAC/C,GAAG,IAAI;wBACP,OAAO;wBACP,WAAW,IAAI;oBACjB,CAAC;gBAED,OAAO;oBAAE,OAAO;gBAAa;YAC/B;QACF;QAEA,iBAAiB,CAAC,KAAK;YACrB,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,IAAI,QAAQ,CAAC,KAAK,EAAE,IAChB;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAC7C;gBAER,CAAC;QACH;QAEA,eAAe,CAAC;YACd,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,IAAI,MAAM;gBACR,MAAM,EAAE,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE,WAAW,GAAG,EAAE,aAAa,IAAI,EAAE,GAAG,UAAU,GAAG;gBACjF,MAAM,OAAO,CAAC;oBACZ,GAAG,QAAQ;oBACX,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC;oBAC3B,QAAQ;gBACV;YACF;QACF;QAEA,aAAa,CAAC;YACZ,MAAM,UAAU,CAAC,IAAI;gBAAE,YAAY;YAAK;QAC1C;QAEA,eAAe,CAAC;YACd,MAAM,UAAU,CAAC,IAAI;gBAAE,YAAY;YAAM;QAC3C;QAEA,mBAAmB;QACnB,aAAa,CAAC;YACZ,MAAM,cAAwB;gBAC5B,GAAG,YAAY;gBACf,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACf;YACA,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;2BAAI,MAAM,UAAU;wBAAE;qBAAY;gBAChD,CAAC;QACH;QAEA,gBAAgB,CAAC,IAAI;YACnB,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAC/B,SAAS,EAAE,KAAK,KAAK;4BAAE,GAAG,QAAQ;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEvD,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;oBAChE,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,UAAU,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,YAAY;wBAAU,IAAI;gBAElE,CAAC;QACH;QAEA,cAAc;QACd,QAAQ,CAAC;YACP,MAAM,SAAc;gBAClB,GAAG,OAAO;gBACV,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACf;YACA,IAAI,CAAA,QAAS,CAAC;oBACZ,MAAM;2BAAI,MAAM,IAAI;wBAAE;qBAAO;gBAC/B,CAAC;QACH;QAEA,WAAW,CAAC,IAAI;YACd,IAAI,CAAA,QAAS,CAAC;oBACZ,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MACnB,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QACH;QAEA,WAAW,CAAC;YACV,IAAI,CAAA,QAAS,CAAC;oBACZ,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBAC1C,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;4BAC9B,GAAG,IAAI;4BACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU;wBAC5C,CAAC;gBACH,CAAC;QACH;QAEA,0BAA0B;QAC1B,WAAW,CAAC;YACV,IAAI,CAAA,QAAS,CAAC;oBACZ,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,GAAG,SAAS;oBAAC;gBAC1C,CAAC;QACH;QAEA,aAAa;YACX,IAAI;gBAAE,QAAQ;YAAc;QAC9B;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;QACb;QAEA,mBAAmB;QACnB,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,UAAU;wBAAE,GAAG,MAAM,QAAQ;wBAAE,GAAG,WAAW;oBAAC;gBAChD,CAAC;QACH;QAEA,oBAAoB;QACpB,aAAa,CAAC;YACZ,IAAI;gBAAE,eAAe;YAAI;QAC3B;QAEA,qBAAqB,CAAC;YACpB,IAAI,CAAA,QAAS,CAAC;oBACZ,eAAe,MAAM,aAAa,CAAC,QAAQ,CAAC,MACxC,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,aAAc,eAAe,MACxD;2BAAI,MAAM,aAAa;wBAAE;qBAAG;gBAClC,CAAC;QACH;QAEA,gBAAgB;YACd,IAAI;gBAAE,eAAe,EAAE;YAAC;QAC1B;QAEA,WAAW;YACT,MAAM,QAAQ,MAAM,KAAK;YACzB,IAAI;gBAAE,eAAe,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;YAAE;QAClD;QAEA,kBAAkB;QAClB,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY,CAAC;YACX,IAAI;gBACF,OAAO,KAAK,KAAK,IAAI,EAAE;gBACvB,YAAY,KAAK,UAAU,IAAI,EAAE;gBACjC,MAAM,KAAK,IAAI,IAAI,EAAE;YACvB;QACF;QAEA,YAAY;YACV,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;YACpC,OAAO;gBAAE;gBAAO;gBAAY;YAAK;QACnC;QAEA,YAAY;YACV,IAAI;gBACF,OAAO,EAAE;gBACT,YAAY,EAAE;gBACd,MAAM,EAAE;gBACR,QAAQ;gBACR,MAAM;gBACN,eAAe,EAAE;gBACjB,WAAW;gBACX,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;YAC5B,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;QAC1B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/SearchModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X, Search } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\n\ninterface SearchModalProps {\n  onClose: () => void;\n}\n\nexport function SearchModal({ onClose }: SearchModalProps) {\n  const [query, setQuery] = useState('');\n  const { setFilter } = useTodoStore();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (query.trim()) {\n      setFilter({ searchQuery: query.trim() });\n    }\n    onClose();\n  };\n\n  const handleClear = () => {\n    setQuery('');\n    setFilter({ searchQuery: undefined });\n    onClose();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 pt-20\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4\">\n        <div className=\"p-4\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              搜索任务\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit}>\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                value={query}\n                onChange={(e) => setQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"搜索任务标题或描述...\"\n                autoFocus\n              />\n            </div>\n\n            <div className=\"flex justify-end space-x-2 mt-4\">\n              <button\n                type=\"button\"\n                onClick={handleClear}\n                className=\"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\"\n              >\n                清除\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n              >\n                搜索\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAUO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEjC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,UAAU;gBAAE,aAAa,MAAM,IAAI;YAAG;QACxC;QACA;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;YAAE,aAAa;QAAU;QACnC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;wCACZ,SAAS;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/FilterModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { Priority, TodoStatus } from '@/types/todo';\n\ninterface FilterModalProps {\n  onClose: () => void;\n}\n\nexport function FilterModal({ onClose }: FilterModalProps) {\n  const { filter, setFilter, clearFilter, categories, tags } = useTodoStore();\n  \n  const [selectedStatus, setSelectedStatus] = useState<TodoStatus[]>(filter.status || []);\n  const [selectedPriority, setSelectedPriority] = useState<Priority[]>(filter.priority || []);\n  const [selectedCategory, setSelectedCategory] = useState<string>(filter.categoryId || '');\n  const [selectedTags, setSelectedTags] = useState<string[]>(filter.tagIds || []);\n  const [showArchived, setShowArchived] = useState<boolean>(filter.showArchived || false);\n\n  const handleApply = () => {\n    setFilter({\n      status: selectedStatus.length > 0 ? selectedStatus : undefined,\n      priority: selectedPriority.length > 0 ? selectedPriority : undefined,\n      categoryId: selectedCategory || undefined,\n      tagIds: selectedTags.length > 0 ? selectedTags : undefined,\n      showArchived,\n    });\n    onClose();\n  };\n\n  const handleClear = () => {\n    clearFilter();\n    onClose();\n  };\n\n  const toggleStatus = (status: TodoStatus) => {\n    setSelectedStatus(prev =>\n      prev.includes(status)\n        ? prev.filter(s => s !== status)\n        : [...prev, status]\n    );\n  };\n\n  const togglePriority = (priority: Priority) => {\n    setSelectedPriority(prev =>\n      prev.includes(priority)\n        ? prev.filter(p => p !== priority)\n        : [...prev, priority]\n    );\n  };\n\n  const toggleTag = (tagId: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tagId)\n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    );\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            筛选任务\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        <div className=\"p-4 space-y-6\">\n          {/* Status Filter */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              状态\n            </h3>\n            <div className=\"space-y-2\">\n              {[\n                { value: 'pending', label: '待处理' },\n                { value: 'in-progress', label: '进行中' },\n                { value: 'completed', label: '已完成' },\n                { value: 'cancelled', label: '已取消' },\n              ].map(status => (\n                <label key={status.value} className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedStatus.includes(status.value as TodoStatus)}\n                    onChange={() => toggleStatus(status.value as TodoStatus)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {status.label}\n                  </span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Priority Filter */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              优先级\n            </h3>\n            <div className=\"space-y-2\">\n              {[\n                { value: 'urgent', label: '紧急' },\n                { value: 'high', label: '高' },\n                { value: 'medium', label: '中' },\n                { value: 'low', label: '低' },\n              ].map(priority => (\n                <label key={priority.value} className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedPriority.includes(priority.value as Priority)}\n                    onChange={() => togglePriority(priority.value as Priority)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {priority.label}\n                  </span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Category Filter */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              分类\n            </h3>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">所有分类</option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.icon} {category.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Tags Filter */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              标签\n            </h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {tags.map(tag => (\n                <button\n                  key={tag.id}\n                  onClick={() => toggleTag(tag.id)}\n                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                    selectedTags.includes(tag.id)\n                      ? 'text-white'\n                      : 'text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'\n                  }`}\n                  style={{\n                    backgroundColor: selectedTags.includes(tag.id) ? tag.color : undefined,\n                  }}\n                >\n                  {tag.name}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Show Archived */}\n          <div>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={showArchived}\n                onChange={(e) => setShowArchived(e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                显示已归档的任务\n              </span>\n            </label>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-2 p-4 border-t border-gray-200 dark:border-gray-700\">\n          <button\n            onClick={handleClear}\n            className=\"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\"\n          >\n            清除筛选\n          </button>\n          <button\n            onClick={handleApply}\n            className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n          >\n            应用筛选\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAExE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,OAAO,MAAM,IAAI,EAAE;IACtF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,OAAO,QAAQ,IAAI,EAAE;IAC1F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,OAAO,UAAU,IAAI;IACtF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,OAAO,MAAM,IAAI,EAAE;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,OAAO,YAAY,IAAI;IAEjF,MAAM,cAAc;QAClB,UAAU;YACR,QAAQ,eAAe,MAAM,GAAG,IAAI,iBAAiB;YACrD,UAAU,iBAAiB,MAAM,GAAG,IAAI,mBAAmB;YAC3D,YAAY,oBAAoB;YAChC,QAAQ,aAAa,MAAM,GAAG,IAAI,eAAe;YACjD;QACF;QACA;IACF;IAEA,MAAM,cAAc;QAClB;QACA;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,UACvB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,YACvB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,SACzB;mBAAI;gBAAM;aAAM;IAExB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAW,OAAO;wCAAM;wCACjC;4CAAE,OAAO;4CAAe,OAAO;wCAAM;wCACrC;4CAAE,OAAO;4CAAa,OAAO;wCAAM;wCACnC;4CAAE,OAAO;4CAAa,OAAO;wCAAM;qCACpC,CAAC,GAAG,CAAC,CAAA,uBACJ,8OAAC;4CAAyB,WAAU;;8DAClC,8OAAC;oDACC,MAAK;oDACL,SAAS,eAAe,QAAQ,CAAC,OAAO,KAAK;oDAC7C,UAAU,IAAM,aAAa,OAAO,KAAK;oDACzC,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DACb,OAAO,KAAK;;;;;;;2CARL,OAAO,KAAK;;;;;;;;;;;;;;;;sCAgB9B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAU,OAAO;wCAAK;wCAC/B;4CAAE,OAAO;4CAAQ,OAAO;wCAAI;wCAC5B;4CAAE,OAAO;4CAAU,OAAO;wCAAI;wCAC9B;4CAAE,OAAO;4CAAO,OAAO;wCAAI;qCAC5B,CAAC,GAAG,CAAC,CAAA,yBACJ,8OAAC;4CAA2B,WAAU;;8DACpC,8OAAC;oDACC,MAAK;oDACL,SAAS,iBAAiB,QAAQ,CAAC,SAAS,KAAK;oDACjD,UAAU,IAAM,eAAe,SAAS,KAAK;oDAC7C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DACb,SAAS,KAAK;;;;;;;2CARP,SAAS,KAAK;;;;;;;;;;;;;;;;sCAgBhC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACnD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;gDAAyB,OAAO,SAAS,EAAE;;oDACzC,SAAS,IAAI;oDAAC;oDAAE,SAAS,IAAI;;+CADnB,SAAS,EAAE;;;;;;;;;;;;;;;;;sCAQ9B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;4CAEC,SAAS,IAAM,UAAU,IAAI,EAAE;4CAC/B,WAAW,CAAC,6DAA6D,EACvE,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,eACA,0GACJ;4CACF,OAAO;gDACL,iBAAiB,aAAa,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,KAAK,GAAG;4CAC/D;sDAEC,IAAI,IAAI;2CAXJ,IAAI,EAAE;;;;;;;;;;;;;;;;sCAkBnB,8OAAC;sCACC,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;wCACjD,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;8BAOtE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Plus, \n  Settings, \n  Search, \n  Filter,\n  Menu,\n  X,\n  MoreHorizontal,\n  Archive,\n  Trash2,\n  CheckSquare\n} from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { SearchModal } from './SearchModal';\nimport { FilterModal } from './FilterModal';\n\ninterface HeaderProps {\n  onAddTodo: () => void;\n  onOpenSettings: () => void;\n  onToggleSidebar: () => void;\n  sidebarOpen: boolean;\n  selectedCount: number;\n}\n\nexport function Header({\n  onAddTodo,\n  onOpenSettings,\n  onToggleSidebar,\n  sidebarOpen,\n  selectedCount,\n}: HeaderProps) {\n  const [showSearchModal, setShowSearchModal] = useState(false);\n  const [showFilterModal, setShowFilterModal] = useState(false);\n  const [showBulkActions, setShowBulkActions] = useState(false);\n\n  const {\n    selectedTodos,\n    bulkUpdateTodos,\n    deleteTodo,\n    clearSelection,\n    filter,\n  } = useTodoStore();\n\n  const handleBulkComplete = () => {\n    bulkUpdateTodos(selectedTodos, { status: 'completed', completedAt: new Date() });\n    clearSelection();\n    setShowBulkActions(false);\n  };\n\n  const handleBulkArchive = () => {\n    bulkUpdateTodos(selectedTodos, { isArchived: true });\n    clearSelection();\n    setShowBulkActions(false);\n  };\n\n  const handleBulkDelete = () => {\n    if (confirm(`确定要删除 ${selectedCount} 个任务吗？此操作无法撤销。`)) {\n      selectedTodos.forEach(id => deleteTodo(id));\n      clearSelection();\n      setShowBulkActions(false);\n    }\n  };\n\n  const hasActiveFilters = Object.keys(filter).some(key => {\n    const value = filter[key as keyof typeof filter];\n    if (key === 'showArchived') return false; // This is not considered an active filter\n    if (Array.isArray(value)) return value.length > 0;\n    if (typeof value === 'string') return value.length > 0;\n    if (typeof value === 'object' && value !== null) {\n      return Object.values(value).some(v => v !== undefined);\n    }\n    return false;\n  });\n\n  return (\n    <>\n      <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Left side */}\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={onToggleSidebar}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              title={sidebarOpen ? '隐藏侧边栏' : '显示侧边栏'}\n            >\n              {sidebarOpen ? (\n                <X className=\"w-5 h-5 text-gray-600 dark:text-gray-300\" />\n              ) : (\n                <Menu className=\"w-5 h-5 text-gray-600 dark:text-gray-300\" />\n              )}\n            </button>\n\n            <div className=\"flex items-center space-x-2\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                高级待办\n              </h1>\n              {selectedCount > 0 && (\n                <span className=\"px-2 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full\">\n                  已选择 {selectedCount} 项\n                </span>\n              )}\n            </div>\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-2\">\n            {selectedCount > 0 ? (\n              // Bulk actions\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  onClick={handleBulkComplete}\n                  className=\"p-2 rounded-lg hover:bg-green-100 dark:hover:bg-green-900 text-green-600 dark:text-green-400 transition-colors\"\n                  title=\"标记为完成\"\n                >\n                  <CheckSquare className=\"w-5 h-5\" />\n                </button>\n                <button\n                  onClick={handleBulkArchive}\n                  className=\"p-2 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900 text-yellow-600 dark:text-yellow-400 transition-colors\"\n                  title=\"归档\"\n                >\n                  <Archive className=\"w-5 h-5\" />\n                </button>\n                <button\n                  onClick={handleBulkDelete}\n                  className=\"p-2 rounded-lg hover:bg-red-100 dark:hover:bg-red-900 text-red-600 dark:text-red-400 transition-colors\"\n                  title=\"删除\"\n                >\n                  <Trash2 className=\"w-5 h-5\" />\n                </button>\n                <button\n                  onClick={clearSelection}\n                  className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n                  title=\"取消选择\"\n                >\n                  <X className=\"w-5 h-5\" />\n                </button>\n              </div>\n            ) : (\n              // Normal actions\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  onClick={() => setShowSearchModal(true)}\n                  className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n                  title=\"搜索任务\"\n                >\n                  <Search className=\"w-5 h-5\" />\n                </button>\n                <button\n                  onClick={() => setShowFilterModal(true)}\n                  className={`p-2 rounded-lg transition-colors ${\n                    hasActiveFilters\n                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'\n                      : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300'\n                  }`}\n                  title=\"筛选任务\"\n                >\n                  <Filter className=\"w-5 h-5\" />\n                </button>\n                <button\n                  onClick={onOpenSettings}\n                  className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n                  title=\"设置\"\n                >\n                  <Settings className=\"w-5 h-5\" />\n                </button>\n                <button\n                  onClick={onAddTodo}\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n                  title=\"添加新任务\"\n                >\n                  <Plus className=\"w-5 h-5\" />\n                  <span className=\"hidden sm:inline\">新任务</span>\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Modals */}\n      {showSearchModal && (\n        <SearchModal onClose={() => setShowSearchModal(false)} />\n      )}\n\n      {showFilterModal && (\n        <FilterModal onClose={() => setShowFilterModal(false)} />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AAjBA;;;;;;;AA2BO,SAAS,OAAO,EACrB,SAAS,EACT,cAAc,EACd,eAAe,EACf,WAAW,EACX,aAAa,EACD;IACZ,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,EACJ,aAAa,EACb,eAAe,EACf,UAAU,EACV,cAAc,EACd,MAAM,EACP,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEf,MAAM,qBAAqB;QACzB,gBAAgB,eAAe;YAAE,QAAQ;YAAa,aAAa,IAAI;QAAO;QAC9E;QACA,mBAAmB;IACrB;IAEA,MAAM,oBAAoB;QACxB,gBAAgB,eAAe;YAAE,YAAY;QAAK;QAClD;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,IAAI,QAAQ,CAAC,MAAM,EAAE,cAAc,cAAc,CAAC,GAAG;YACnD,cAAc,OAAO,CAAC,CAAA,KAAM,WAAW;YACvC;YACA,mBAAmB;QACrB;IACF;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;QAChD,MAAM,QAAQ,MAAM,CAAC,IAA2B;QAChD,IAAI,QAAQ,gBAAgB,OAAO,OAAO,0CAA0C;QACpF,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,MAAM,MAAM,GAAG;QAChD,IAAI,OAAO,UAAU,UAAU,OAAO,MAAM,MAAM,GAAG;QACrD,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;YAC/C,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,MAAM;QAC9C;QACA,OAAO;IACT;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO,cAAc,UAAU;8CAE9B,4BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAIpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;wCAGhE,gBAAgB,mBACf,8OAAC;4CAAK,WAAU;;gDAA+F;gDACxG;gDAAc;;;;;;;;;;;;;;;;;;;sCAO3B,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,IACf,eAAe;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;uCAIjB,iBAAiB;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,iCAAiC,EAC3C,mBACI,kEACA,6EACJ;wCACF,OAAM;kDAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;;0DAEN,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS9C,iCACC,8OAAC,iIAAA,CAAA,cAAW;gBAAC,SAAS,IAAM,mBAAmB;;;;;;YAGhD,iCACC,8OAAC,iIAAA,CAAA,cAAW;gBAAC,SAAS,IAAM,mBAAmB;;;;;;;;AAIvD", "debugId": null}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  Calendar,\n  CheckSquare,\n  Clock,\n  Star,\n  Archive,\n  Tag,\n  Folder,\n  Plus,\n  MoreHorizontal,\n  Edit,\n  Trash2,\n} from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { Category, Tag as TagType } from '@/types/todo';\nimport { getCompletionPercentage } from '@/lib/utils';\n\ninterface SidebarProps {\n  categories: Category[];\n  tags: TagType[];\n  onClose: () => void;\n}\n\nexport function Sidebar({ categories, tags, onClose }: SidebarProps) {\n  const [showCategoryForm, setShowCategoryForm] = useState(false);\n  const [showTagForm, setShowTagForm] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [editingTag, setEditingTag] = useState<TagType | null>(null);\n\n  const {\n    todos,\n    filter,\n    setFilter,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    addTag,\n    updateTag,\n    deleteTag,\n  } = useTodoStore();\n\n  const todayTodos = todos.filter(todo => {\n    if (!todo.dueDate) return false;\n    const today = new Date();\n    const dueDate = new Date(todo.dueDate);\n    return dueDate.toDateString() === today.toDateString();\n  });\n\n  const upcomingTodos = todos.filter(todo => {\n    if (!todo.dueDate) return false;\n    const today = new Date();\n    const dueDate = new Date(todo.dueDate);\n    const diffTime = dueDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays > 0 && diffDays <= 7;\n  });\n\n  const completedTodos = todos.filter(todo => todo.status === 'completed');\n  const archivedTodos = todos.filter(todo => todo.isArchived);\n\n  const handleQuickFilter = (filterType: string, value?: any) => {\n    switch (filterType) {\n      case 'all':\n        setFilter({});\n        break;\n      case 'today':\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        setFilter({\n          dueDateRange: {\n            start: today,\n            end: tomorrow,\n          },\n        });\n        break;\n      case 'upcoming':\n        const now = new Date();\n        const nextWeek = new Date();\n        nextWeek.setDate(nextWeek.getDate() + 7);\n        setFilter({\n          dueDateRange: {\n            start: now,\n            end: nextWeek,\n          },\n        });\n        break;\n      case 'completed':\n        setFilter({ status: ['completed'] });\n        break;\n      case 'archived':\n        setFilter({ showArchived: true });\n        break;\n      case 'category':\n        setFilter({ categoryId: value });\n        break;\n      case 'tag':\n        setFilter({ tagIds: [value] });\n        break;\n      case 'priority':\n        setFilter({ priority: [value] });\n        break;\n    }\n  };\n\n  const handleCategorySubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const name = formData.get('name') as string;\n    const color = formData.get('color') as string;\n    const icon = formData.get('icon') as string;\n\n    if (editingCategory) {\n      updateCategory(editingCategory.id, { name, color, icon });\n      setEditingCategory(null);\n    } else {\n      addCategory({ name, color, icon });\n    }\n    setShowCategoryForm(false);\n  };\n\n  const handleTagSubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const name = formData.get('name') as string;\n    const color = formData.get('color') as string;\n\n    if (editingTag) {\n      updateTag(editingTag.id, { name, color });\n      setEditingTag(null);\n    } else {\n      addTag({ name, color });\n    }\n    setShowTagForm(false);\n  };\n\n  return (\n    <div className=\"h-full bg-white dark:bg-gray-800 flex flex-col\">\n      {/* Quick Filters */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <h2 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-3\">\n          快速筛选\n        </h2>\n        <nav className=\"space-y-1\">\n          <button\n            onClick={() => handleQuickFilter('all')}\n            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n              Object.keys(filter).length === 0 || (Object.keys(filter).length === 1 && filter.showArchived === false)\n                ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'\n                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'\n            }`}\n          >\n            <CheckSquare className=\"w-4 h-4\" />\n            <span>所有任务</span>\n            <span className=\"ml-auto text-sm text-gray-500\">\n              {todos.filter(t => !t.isArchived).length}\n            </span>\n          </button>\n\n          <button\n            onClick={() => handleQuickFilter('today')}\n            className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors\"\n          >\n            <Calendar className=\"w-4 h-4\" />\n            <span>今天</span>\n            <span className=\"ml-auto text-sm text-gray-500\">\n              {todayTodos.length}\n            </span>\n          </button>\n\n          <button\n            onClick={() => handleQuickFilter('upcoming')}\n            className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors\"\n          >\n            <Clock className=\"w-4 h-4\" />\n            <span>即将到期</span>\n            <span className=\"ml-auto text-sm text-gray-500\">\n              {upcomingTodos.length}\n            </span>\n          </button>\n\n          <button\n            onClick={() => handleQuickFilter('completed')}\n            className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors\"\n          >\n            <CheckSquare className=\"w-4 h-4\" />\n            <span>已完成</span>\n            <span className=\"ml-auto text-sm text-gray-500\">\n              {completedTodos.length}\n            </span>\n          </button>\n\n          <button\n            onClick={() => handleQuickFilter('archived')}\n            className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors\"\n          >\n            <Archive className=\"w-4 h-4\" />\n            <span>已归档</span>\n            <span className=\"ml-auto text-sm text-gray-500\">\n              {archivedTodos.length}\n            </span>\n          </button>\n        </nav>\n      </div>\n\n      {/* Categories */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <h2 className=\"text-sm font-semibold text-gray-900 dark:text-white\">\n            分类\n          </h2>\n          <button\n            onClick={() => setShowCategoryForm(true)}\n            className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"\n          >\n            <Plus className=\"w-4 h-4\" />\n          </button>\n        </div>\n\n        {showCategoryForm && (\n          <form onSubmit={handleCategorySubmit} className=\"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n            <input\n              name=\"name\"\n              placeholder=\"分类名称\"\n              defaultValue={editingCategory?.name}\n              className=\"w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              required\n            />\n            <input\n              name=\"icon\"\n              placeholder=\"图标 (emoji)\"\n              defaultValue={editingCategory?.icon}\n              className=\"w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n            />\n            <input\n              name=\"color\"\n              type=\"color\"\n              defaultValue={editingCategory?.color || '#3b82f6'}\n              className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg\"\n            />\n            <div className=\"flex space-x-2 mt-2\">\n              <button\n                type=\"submit\"\n                className=\"px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700\"\n              >\n                {editingCategory ? '更新' : '添加'}\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setShowCategoryForm(false);\n                  setEditingCategory(null);\n                }}\n                className=\"px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-400 dark:hover:bg-gray-500\"\n              >\n                取消\n              </button>\n            </div>\n          </form>\n        )}\n\n        <nav className=\"space-y-1\">\n          {categories.map(category => {\n            const categoryTodos = todos.filter(todo => todo.categoryId === category.id && !todo.isArchived);\n            return (\n              <div\n                key={category.id}\n                className=\"group flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <button\n                  onClick={() => handleQuickFilter('category', category.id)}\n                  className=\"flex-1 flex items-center space-x-3 text-left\"\n                >\n                  <span className=\"text-lg\">{category.icon || '📁'}</span>\n                  <span className=\"text-gray-700 dark:text-gray-300\">{category.name}</span>\n                  <span className=\"ml-auto text-sm text-gray-500\">\n                    {categoryTodos.length}\n                  </span>\n                </button>\n                <div className=\"opacity-0 group-hover:opacity-100 flex space-x-1\">\n                  <button\n                    onClick={() => {\n                      setEditingCategory(category);\n                      setShowCategoryForm(true);\n                    }}\n                    className=\"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-500 dark:text-gray-400\"\n                  >\n                    <Edit className=\"w-3 h-3\" />\n                  </button>\n                  <button\n                    onClick={() => {\n                      if (confirm('确定要删除这个分类吗？')) {\n                        deleteCategory(category.id);\n                      }\n                    }}\n                    className=\"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-red-500\"\n                  >\n                    <Trash2 className=\"w-3 h-3\" />\n                  </button>\n                </div>\n              </div>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tags */}\n      <div className=\"p-4 flex-1 overflow-y-auto\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <h2 className=\"text-sm font-semibold text-gray-900 dark:text-white\">\n            标签\n          </h2>\n          <button\n            onClick={() => setShowTagForm(true)}\n            className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"\n          >\n            <Plus className=\"w-4 h-4\" />\n          </button>\n        </div>\n\n        {showTagForm && (\n          <form onSubmit={handleTagSubmit} className=\"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n            <input\n              name=\"name\"\n              placeholder=\"标签名称\"\n              defaultValue={editingTag?.name}\n              className=\"w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              required\n            />\n            <input\n              name=\"color\"\n              type=\"color\"\n              defaultValue={editingTag?.color || '#3b82f6'}\n              className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg\"\n            />\n            <div className=\"flex space-x-2 mt-2\">\n              <button\n                type=\"submit\"\n                className=\"px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700\"\n              >\n                {editingTag ? '更新' : '添加'}\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setShowTagForm(false);\n                  setEditingTag(null);\n                }}\n                className=\"px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-400 dark:hover:bg-gray-500\"\n              >\n                取消\n              </button>\n            </div>\n          </form>\n        )}\n\n        <div className=\"space-y-1\">\n          {tags.map(tag => {\n            const tagTodos = todos.filter(todo => todo.tags.includes(tag.id) && !todo.isArchived);\n            return (\n              <div\n                key={tag.id}\n                className=\"group flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <button\n                  onClick={() => handleQuickFilter('tag', tag.id)}\n                  className=\"flex-1 flex items-center space-x-3 text-left\"\n                >\n                  <div\n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: tag.color }}\n                  />\n                  <span className=\"text-gray-700 dark:text-gray-300\">{tag.name}</span>\n                  <span className=\"ml-auto text-sm text-gray-500\">\n                    {tagTodos.length}\n                  </span>\n                </button>\n                <div className=\"opacity-0 group-hover:opacity-100 flex space-x-1\">\n                  <button\n                    onClick={() => {\n                      setEditingTag(tag);\n                      setShowTagForm(true);\n                    }}\n                    className=\"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-500 dark:text-gray-400\"\n                  >\n                    <Edit className=\"w-3 h-3\" />\n                  </button>\n                  <button\n                    onClick={() => {\n                      if (confirm('确定要删除这个标签吗？')) {\n                        deleteTag(tag.id);\n                      }\n                    }}\n                    className=\"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-red-500\"\n                  >\n                    <Trash2 className=\"w-3 h-3\" />\n                  </button>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Progress */}\n      <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n          总体进度\n        </div>\n        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div\n            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${getCompletionPercentage(todos.filter(t => !t.isArchived))}%` }}\n          />\n        </div>\n        <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          {getCompletionPercentage(todos.filter(t => !t.isArchived))}% 完成\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAEA;AAlBA;;;;;;AA0BO,SAAS,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAgB;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE7D,MAAM,EACJ,KAAK,EACL,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,cAAc,EACd,MAAM,EACN,SAAS,EACT,SAAS,EACV,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEf,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA;QAC9B,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;QAC1B,MAAM,QAAQ,IAAI;QAClB,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO;QACrC,OAAO,QAAQ,YAAY,OAAO,MAAM,YAAY;IACtD;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;QAC1B,MAAM,QAAQ,IAAI;QAClB,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO;QACrC,MAAM,WAAW,QAAQ,OAAO,KAAK,MAAM,OAAO;QAClD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,WAAW,KAAK,YAAY;IACrC;IAEA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAC5D,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU;IAE1D,MAAM,oBAAoB,CAAC,YAAoB;QAC7C,OAAQ;YACN,KAAK;gBACH,UAAU,CAAC;gBACX;YACF,KAAK;gBACH,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;gBACxB,MAAM,WAAW,IAAI,KAAK;gBAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;gBACtC,UAAU;oBACR,cAAc;wBACZ,OAAO;wBACP,KAAK;oBACP;gBACF;gBACA;YACF,KAAK;gBACH,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,IAAI;gBACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;gBACtC,UAAU;oBACR,cAAc;wBACZ,OAAO;wBACP,KAAK;oBACP;gBACF;gBACA;YACF,KAAK;gBACH,UAAU;oBAAE,QAAQ;wBAAC;qBAAY;gBAAC;gBAClC;YACF,KAAK;gBACH,UAAU;oBAAE,cAAc;gBAAK;gBAC/B;YACF,KAAK;gBACH,UAAU;oBAAE,YAAY;gBAAM;gBAC9B;YACF,KAAK;gBACH,UAAU;oBAAE,QAAQ;wBAAC;qBAAM;gBAAC;gBAC5B;YACF,KAAK;gBACH,UAAU;oBAAE,UAAU;wBAAC;qBAAM;gBAAC;gBAC9B;QACJ;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,QAAQ,SAAS,GAAG,CAAC;QAC3B,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,iBAAiB;YACnB,eAAe,gBAAgB,EAAE,EAAE;gBAAE;gBAAM;gBAAO;YAAK;YACvD,mBAAmB;QACrB,OAAO;YACL,YAAY;gBAAE;gBAAM;gBAAO;YAAK;QAClC;QACA,oBAAoB;IACtB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,QAAQ,SAAS,GAAG,CAAC;QAE3B,IAAI,YAAY;YACd,UAAU,WAAW,EAAE,EAAE;gBAAE;gBAAM;YAAM;YACvC,cAAc;QAChB,OAAO;YACL,OAAO;gBAAE;gBAAM;YAAM;QACvB;QACA,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,oFAAoF,EAC9F,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,KAAM,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,KAAK,OAAO,YAAY,KAAK,QAC7F,kEACA,6EACJ;;kDAEF,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDACb,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;;;;;;;;;;;;0CAI5C,8OAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDACb,WAAW,MAAM;;;;;;;;;;;;0CAItB,8OAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDACb,cAAc,MAAM;;;;;;;;;;;;0CAIzB,8OAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDACb,eAAe,MAAM;;;;;;;;;;;;0CAI1B,8OAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDACb,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAInB,kCACC,8OAAC;wBAAK,UAAU;wBAAsB,WAAU;;0CAC9C,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,cAAc,iBAAiB;gCAC/B,WAAU;gCACV,QAAQ;;;;;;0CAEV,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,cAAc,iBAAiB;gCAC/B,WAAU;;;;;;0CAEZ,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,cAAc,iBAAiB,SAAS;gCACxC,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDAET,kBAAkB,OAAO;;;;;;kDAE5B,8OAAC;wCACC,MAAK;wCACL,SAAS;4CACP,oBAAoB;4CACpB,mBAAmB;wCACrB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA;4BACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,SAAS,EAAE,IAAI,CAAC,KAAK,UAAU;4BAC9F,qBACE,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCACC,SAAS,IAAM,kBAAkB,YAAY,SAAS,EAAE;wCACxD,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAW,SAAS,IAAI,IAAI;;;;;;0DAC5C,8OAAC;gDAAK,WAAU;0DAAoC,SAAS,IAAI;;;;;;0DACjE,8OAAC;gDAAK,WAAU;0DACb,cAAc,MAAM;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP,mBAAmB;oDACnB,oBAAoB;gDACtB;gDACA,WAAU;0DAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDACC,SAAS;oDACP,IAAI,QAAQ,gBAAgB;wDAC1B,eAAe,SAAS,EAAE;oDAC5B;gDACF;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;+BA/BjB,SAAS,EAAE;;;;;wBAoCtB;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAInB,6BACC,8OAAC;wBAAK,UAAU;wBAAiB,WAAU;;0CACzC,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,cAAc,YAAY;gCAC1B,WAAU;gCACV,QAAQ;;;;;;0CAEV,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,cAAc,YAAY,SAAS;gCACnC,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDAET,aAAa,OAAO;;;;;;kDAEvB,8OAAC;wCACC,MAAK;wCACL,SAAS;4CACP,eAAe;4CACf,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA;4BACR,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,UAAU;4BACpF,qBACE,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCACC,SAAS,IAAM,kBAAkB,OAAO,IAAI,EAAE;wCAC9C,WAAU;;0DAEV,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,IAAI,KAAK;gDAAC;;;;;;0DAEtC,8OAAC;gDAAK,WAAU;0DAAoC,IAAI,IAAI;;;;;;0DAC5D,8OAAC;gDAAK,WAAU;0DACb,SAAS,MAAM;;;;;;;;;;;;kDAGpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP,cAAc;oDACd,eAAe;gDACjB;gDACA,WAAU;0DAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDACC,SAAS;oDACP,IAAI,QAAQ,gBAAgB;wDAC1B,UAAU,IAAI,EAAE;oDAClB;gDACF;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;+BAlCjB,IAAI,EAAE;;;;;wBAuCjB;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgD;;;;;;kCAG/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC;4BAAC;;;;;;;;;;;kCAGpF,8OAAC;wBAAI,WAAU;;4BACZ,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU;4BAAG;;;;;;;;;;;;;;;;;;;AAKrE", "debugId": null}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/TodoItem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport {\n  Calendar,\n  Clock,\n  Flag,\n  MoreHorizontal,\n  Edit,\n  Trash2,\n  Archive,\n  Copy,\n  CheckSquare,\n  Square,\n  GripVertical,\n  MessageSquare,\n  Paperclip,\n} from 'lucide-react';\nimport { Todo, Category, Tag } from '@/types/todo';\nimport { useTodoStore } from '@/store/todoStore';\nimport { formatDate, getPriorityColor, getPriorityLabel, getStatusColor, isOverdue, isDueSoon } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\n\ninterface TodoItemProps {\n  todo: Todo;\n  categories: Category[];\n  tags: Tag[];\n  compact?: boolean;\n}\n\nexport function TodoItem({ todo, categories, tags, compact = false }: TodoItemProps) {\n  const [showActions, setShowActions] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n\n  const {\n    toggleTodo,\n    updateTodo,\n    deleteTodo,\n    duplicateTodo,\n    archiveTodo,\n    selectedTodos,\n    toggleTodoSelection,\n  } = useTodoStore();\n\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: todo.id });\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  };\n\n  const category = categories.find(c => c.id === todo.categoryId);\n  const todoTags = tags.filter(tag => todo.tags.includes(tag.id));\n  const isSelected = selectedTodos.includes(todo.id);\n  const isCompleted = todo.status === 'completed';\n  const hasSubtasks = todo.subtasks.length > 0;\n  const completedSubtasks = todo.subtasks.filter(st => st.completed).length;\n\n  const handleToggle = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    toggleTodo(todo.id);\n  };\n\n  const handleSelect = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    toggleTodoSelection(todo.id);\n  };\n\n  const handleAction = (action: string, e: React.MouseEvent) => {\n    e.stopPropagation();\n    setShowActions(false);\n    \n    switch (action) {\n      case 'edit':\n        // TODO: Open edit modal\n        break;\n      case 'duplicate':\n        duplicateTodo(todo.id);\n        break;\n      case 'archive':\n        archiveTodo(todo.id);\n        break;\n      case 'delete':\n        if (confirm('确定要删除这个任务吗？')) {\n          deleteTodo(todo.id);\n        }\n        break;\n    }\n  };\n\n  const priorityColor = getPriorityColor(todo.priority);\n  const statusColor = getStatusColor(todo.status);\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      className={cn(\n        'group relative bg-white dark:bg-gray-800 transition-all duration-200',\n        {\n          'opacity-50': isDragging,\n          'ring-2 ring-blue-500': isSelected,\n          'opacity-60': isCompleted,\n          'border-l-4': !compact,\n        },\n        compact ? 'p-4' : 'p-6',\n        !compact && 'hover:bg-gray-50 dark:hover:bg-gray-700'\n      )}\n      style={{\n        ...style,\n        borderLeftColor: category?.color || '#e5e7eb',\n      }}\n      onClick={() => !compact && setShowDetails(!showDetails)}\n    >\n      {/* Drag Handle */}\n      {!compact && (\n        <div\n          {...attributes}\n          {...listeners}\n          className=\"absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 cursor-grab active:cursor-grabbing\"\n        >\n          <GripVertical className=\"w-4 h-4 text-gray-400\" />\n        </div>\n      )}\n\n      <div className={cn('flex items-start space-x-3', { 'ml-6': !compact })}>\n        {/* Checkbox */}\n        <button\n          onClick={handleToggle}\n          className={cn(\n            'flex-shrink-0 mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors',\n            isCompleted\n              ? 'bg-green-500 border-green-500 text-white'\n              : 'border-gray-300 dark:border-gray-600 hover:border-green-500'\n          )}\n        >\n          {isCompleted ? (\n            <CheckSquare className=\"w-3 h-3\" />\n          ) : (\n            <Square className=\"w-3 h-3 opacity-0\" />\n          )}\n        </button>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          {/* Title and Priority */}\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1\">\n              <h3\n                className={cn(\n                  'text-sm font-medium text-gray-900 dark:text-white',\n                  { 'line-through text-gray-500 dark:text-gray-400': isCompleted }\n                )}\n              >\n                {todo.title}\n              </h3>\n              {todo.description && (\n                <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2\">\n                  {todo.description}\n                </p>\n              )}\n            </div>\n\n            {/* Priority Badge */}\n            <span className={cn('ml-2 px-2 py-1 text-xs font-medium rounded-full border', priorityColor)}>\n              {getPriorityLabel(todo.priority)}\n            </span>\n          </div>\n\n          {/* Meta Information */}\n          <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n            {/* Category */}\n            {category && (\n              <div className=\"flex items-center space-x-1\">\n                <span>{category.icon || '📁'}</span>\n                <span>{category.name}</span>\n              </div>\n            )}\n\n            {/* Due Date */}\n            {todo.dueDate && (\n              <div\n                className={cn('flex items-center space-x-1', {\n                  'text-red-600 dark:text-red-400': isOverdue(todo.dueDate),\n                  'text-yellow-600 dark:text-yellow-400': isDueSoon(todo.dueDate),\n                })}\n              >\n                <Calendar className=\"w-3 h-3\" />\n                <span>{formatDate(todo.dueDate)}</span>\n              </div>\n            )}\n\n            {/* Subtasks */}\n            {hasSubtasks && (\n              <div className=\"flex items-center space-x-1\">\n                <CheckSquare className=\"w-3 h-3\" />\n                <span>{completedSubtasks}/{todo.subtasks.length}</span>\n              </div>\n            )}\n\n            {/* Attachments */}\n            {todo.attachments.length > 0 && (\n              <div className=\"flex items-center space-x-1\">\n                <Paperclip className=\"w-3 h-3\" />\n                <span>{todo.attachments.length}</span>\n              </div>\n            )}\n\n            {/* Comments/Notes */}\n            {todo.description && (\n              <div className=\"flex items-center space-x-1\">\n                <MessageSquare className=\"w-3 h-3\" />\n              </div>\n            )}\n          </div>\n\n          {/* Tags */}\n          {todoTags.length > 0 && (\n            <div className=\"mt-2 flex flex-wrap gap-1\">\n              {todoTags.map(tag => (\n                <span\n                  key={tag.id}\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium rounded-full text-white\"\n                  style={{ backgroundColor: tag.color }}\n                >\n                  {tag.name}\n                </span>\n              ))}\n            </div>\n          )}\n\n          {/* Subtasks Preview */}\n          {showDetails && hasSubtasks && (\n            <div className=\"mt-3 space-y-1\">\n              {todo.subtasks.slice(0, 3).map(subtask => (\n                <div key={subtask.id} className=\"flex items-center space-x-2 text-sm\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      const updatedSubtasks = todo.subtasks.map(st =>\n                        st.id === subtask.id ? { ...st, completed: !st.completed } : st\n                      );\n                      updateTodo(todo.id, { subtasks: updatedSubtasks });\n                    }}\n                    className={cn(\n                      'w-4 h-4 rounded border flex items-center justify-center',\n                      subtask.completed\n                        ? 'bg-green-500 border-green-500 text-white'\n                        : 'border-gray-300 dark:border-gray-600'\n                    )}\n                  >\n                    {subtask.completed && <CheckSquare className=\"w-2 h-2\" />}\n                  </button>\n                  <span\n                    className={cn(\n                      'text-gray-700 dark:text-gray-300',\n                      { 'line-through text-gray-500 dark:text-gray-400': subtask.completed }\n                    )}\n                  >\n                    {subtask.title}\n                  </span>\n                </div>\n              ))}\n              {todo.subtasks.length > 3 && (\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  还有 {todo.subtasks.length - 3} 个子任务...\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex items-center space-x-1\">\n          {/* Selection Checkbox */}\n          <button\n            onClick={handleSelect}\n            className={cn(\n              'w-4 h-4 rounded border flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity',\n              isSelected\n                ? 'bg-blue-500 border-blue-500 text-white opacity-100'\n                : 'border-gray-300 dark:border-gray-600 hover:border-blue-500'\n            )}\n          >\n            {isSelected && <CheckSquare className=\"w-2 h-2\" />}\n          </button>\n\n          {/* More Actions */}\n          <div className=\"relative\">\n            <button\n              onClick={(e) => {\n                e.stopPropagation();\n                setShowActions(!showActions);\n              }}\n              className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity\"\n            >\n              <MoreHorizontal className=\"w-4 h-4 text-gray-500 dark:text-gray-400\" />\n            </button>\n\n            {showActions && (\n              <div className=\"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10\">\n                <div className=\"py-1\">\n                  <button\n                    onClick={(e) => handleAction('edit', e)}\n                    className=\"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n                  >\n                    <Edit className=\"w-4 h-4\" />\n                    <span>编辑</span>\n                  </button>\n                  <button\n                    onClick={(e) => handleAction('duplicate', e)}\n                    className=\"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n                  >\n                    <Copy className=\"w-4 h-4\" />\n                    <span>复制</span>\n                  </button>\n                  <button\n                    onClick={(e) => handleAction('archive', e)}\n                    className=\"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n                  >\n                    <Archive className=\"w-4 h-4\" />\n                    <span>归档</span>\n                  </button>\n                  <button\n                    onClick={(e) => handleAction('delete', e)}\n                    className=\"w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n                  >\n                    <Trash2 className=\"w-4 h-4\" />\n                    <span>删除</span>\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AAtBA;;;;;;;;;AAgCO,SAAS,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,KAAK,EAAiB;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,UAAU,EACV,UAAU,EACV,UAAU,EACV,aAAa,EACb,WAAW,EACX,aAAa,EACb,mBAAmB,EACpB,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEf,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,KAAK,EAAE;IAAC;IAE9B,MAAM,QAAQ;QACZ,WAAW,qKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,UAAU;IAC9D,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,MAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;IAC7D,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;IACjD,MAAM,cAAc,KAAK,MAAM,KAAK;IACpC,MAAM,cAAc,KAAK,QAAQ,CAAC,MAAM,GAAG;IAC3C,MAAM,oBAAoB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,KAAM,GAAG,SAAS,EAAE,MAAM;IAEzE,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,WAAW,KAAK,EAAE;IACpB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,oBAAoB,KAAK,EAAE;IAC7B;IAEA,MAAM,eAAe,CAAC,QAAgB;QACpC,EAAE,eAAe;QACjB,eAAe;QAEf,OAAQ;YACN,KAAK;gBAEH;YACF,KAAK;gBACH,cAAc,KAAK,EAAE;gBACrB;YACF,KAAK;gBACH,YAAY,KAAK,EAAE;gBACnB;YACF,KAAK;gBACH,IAAI,QAAQ,gBAAgB;oBAC1B,WAAW,KAAK,EAAE;gBACpB;gBACA;QACJ;IACF;IAEA,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ;IACpD,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM;IAE9C,qBACE,8OAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;YACE,cAAc;YACd,wBAAwB;YACxB,cAAc;YACd,cAAc,CAAC;QACjB,GACA,UAAU,QAAQ,OAClB,CAAC,WAAW;QAEd,OAAO;YACL,GAAG,KAAK;YACR,iBAAiB,UAAU,SAAS;QACtC;QACA,SAAS,IAAM,CAAC,WAAW,eAAe,CAAC;;YAG1C,CAAC,yBACA,8OAAC;gBACE,GAAG,UAAU;gBACb,GAAG,SAAS;gBACb,WAAU;0BAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAI5B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;oBAAE,QAAQ,CAAC;gBAAQ;;kCAElE,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,cACI,6CACA;kCAGL,4BACC,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;oDAAE,iDAAiD;gDAAY;0DAGhE,KAAK,KAAK;;;;;;4CAEZ,KAAK,WAAW,kBACf,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;kDAMvB,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;kDAC3E,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAM,SAAS,IAAI,IAAI;;;;;;0DACxB,8OAAC;0DAAM,SAAS,IAAI;;;;;;;;;;;;oCAKvB,KAAK,OAAO,kBACX,8OAAC;wCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;4CAC3C,kCAAkC,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO;4CACxD,wCAAwC,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO;wCAChE;;0DAEA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;oCAKjC,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;;oDAAM;oDAAkB;oDAAE,KAAK,QAAQ,CAAC,MAAM;;;;;;;;;;;;;oCAKlD,KAAK,WAAW,CAAC,MAAM,GAAG,mBACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAM,KAAK,WAAW,CAAC,MAAM;;;;;;;;;;;;oCAKjC,KAAK,WAAW,kBACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAM9B,SAAS,MAAM,GAAG,mBACjB,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAA,oBACZ,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,IAAI,KAAK;wCAAC;kDAEnC,IAAI,IAAI;uCAJJ,IAAI,EAAE;;;;;;;;;;4BAWlB,eAAe,6BACd,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,wBAC7B,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,MAAM,kBAAkB,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,KACxC,GAAG,EAAE,KAAK,QAAQ,EAAE,GAAG;gEAAE,GAAG,EAAE;gEAAE,WAAW,CAAC,GAAG,SAAS;4DAAC,IAAI;wDAE/D,WAAW,KAAK,EAAE,EAAE;4DAAE,UAAU;wDAAgB;oDAClD;oDACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA,QAAQ,SAAS,GACb,6CACA;8DAGL,QAAQ,SAAS,kBAAI,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAE/C,8OAAC;oDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA;wDAAE,iDAAiD,QAAQ,SAAS;oDAAC;8DAGtE,QAAQ,KAAK;;;;;;;2CAxBR,QAAQ,EAAE;;;;;oCA4BrB,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAU;;4CAA2C;4CACpD,KAAK,QAAQ,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA,aACI,uDACA;0CAGL,4BAAc,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,eAAe,CAAC;wCAClB;wCACA,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;oCAG3B,6BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,CAAC,IAAM,aAAa,QAAQ;oDACrC,WAAU;;sEAEV,8OAAC,2MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,SAAS,CAAC,IAAM,aAAa,aAAa;oDAC1C,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,SAAS,CAAC,IAAM,aAAa,WAAW;oDACxC,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,SAAS,CAAC,IAAM,aAAa,UAAU;oDACvC,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 2888, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/EmptyState.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckSquare, Plus } from 'lucide-react';\n\nexport function EmptyState() {\n  return (\n    <div className=\"flex flex-col items-center justify-center h-full py-12\">\n      <div className=\"text-center\">\n        <CheckSquare className=\"w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n          暂无任务\n        </h3>\n        <p className=\"text-gray-500 dark:text-gray-400 mb-6 max-w-sm\">\n          看起来您还没有任何任务。点击上方的\"新任务\"按钮来创建您的第一个任务吧！\n        </p>\n        <div className=\"flex items-center justify-center space-x-2 text-sm text-gray-400 dark:text-gray-500\">\n          <Plus className=\"w-4 h-4\" />\n          <span>点击\"新任务\"开始</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAIO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAG,WAAU;8BAAyD;;;;;;8BAGvE,8OAAC;oBAAE,WAAU;8BAAiD;;;;;;8BAG9D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 2967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/TodoList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  DndContext,\n  closestCenter,\n  KeyboardSensor,\n  PointerSensor,\n  useSensor,\n  useSensors,\n  DragEndEvent,\n} from '@dnd-kit/core';\nimport {\n  arrayMove,\n  SortableContext,\n  sortableKeyboardCoordinates,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport { Todo, Category, Tag } from '@/types/todo';\nimport { useTodoStore } from '@/store/todoStore';\nimport { TodoItem } from './TodoItem';\nimport { EmptyState } from './EmptyState';\n\ninterface TodoListProps {\n  todos: Todo[];\n  categories: Category[];\n  tags: Tag[];\n  view: 'list' | 'grid' | 'kanban';\n}\n\nexport function TodoList({ todos, categories, tags, view }: TodoListProps) {\n  const { reorderTodos } = useTodoStore();\n  \n  const sensors = useSensors(\n    useSensor(PointerSensor, {\n      activationConstraint: {\n        distance: 8,\n      },\n    }),\n    useSensor(KeyboardSensor, {\n      coordinateGetter: sortableKeyboardCoordinates,\n    })\n  );\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event;\n\n    if (over && active.id !== over.id) {\n      const oldIndex = todos.findIndex(todo => todo.id === active.id);\n      const newIndex = todos.findIndex(todo => todo.id === over.id);\n      \n      if (oldIndex !== -1 && newIndex !== -1) {\n        reorderTodos(oldIndex, newIndex);\n      }\n    }\n  };\n\n  if (todos.length === 0) {\n    return <EmptyState />;\n  }\n\n  const renderListView = () => (\n    <DndContext\n      sensors={sensors}\n      collisionDetection={closestCenter}\n      onDragEnd={handleDragEnd}\n    >\n      <SortableContext items={todos.map(todo => todo.id)} strategy={verticalListSortingStrategy}>\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {todos.map(todo => (\n            <TodoItem\n              key={todo.id}\n              todo={todo}\n              categories={categories}\n              tags={tags}\n            />\n          ))}\n        </div>\n      </SortableContext>\n    </DndContext>\n  );\n\n  const renderGridView = () => (\n    <DndContext\n      sensors={sensors}\n      collisionDetection={closestCenter}\n      onDragEnd={handleDragEnd}\n    >\n      <SortableContext items={todos.map(todo => todo.id)}>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6\">\n          {todos.map(todo => (\n            <div key={todo.id} className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\n              <TodoItem\n                todo={todo}\n                categories={categories}\n                tags={tags}\n                compact\n              />\n            </div>\n          ))}\n        </div>\n      </SortableContext>\n    </DndContext>\n  );\n\n  const renderKanbanView = () => {\n    const statusColumns = [\n      { key: 'pending', title: '待处理', todos: todos.filter(t => t.status === 'pending') },\n      { key: 'in-progress', title: '进行中', todos: todos.filter(t => t.status === 'in-progress') },\n      { key: 'completed', title: '已完成', todos: todos.filter(t => t.status === 'completed') },\n    ];\n\n    return (\n      <div className=\"flex space-x-6 p-6 h-full overflow-x-auto\">\n        {statusColumns.map(column => (\n          <div key={column.key} className=\"flex-shrink-0 w-80\">\n            <div className=\"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\">\n              <h3 className=\"font-semibold text-gray-900 dark:text-white mb-4 flex items-center justify-between\">\n                {column.title}\n                <span className=\"text-sm text-gray-500 bg-white dark:bg-gray-600 px-2 py-1 rounded-full\">\n                  {column.todos.length}\n                </span>\n              </h3>\n              <div className=\"space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto\">\n                {column.todos.map(todo => (\n                  <div key={todo.id} className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm\">\n                    <TodoItem\n                      todo={todo}\n                      categories={categories}\n                      tags={tags}\n                      compact\n                    />\n                  </div>\n                ))}\n                {column.todos.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                    暂无任务\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"h-full overflow-auto\">\n      {view === 'list' && renderListView()}\n      {view === 'grid' && renderGridView()}\n      {view === 'kanban' && renderKanbanView()}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AASA;AAOA;AACA;AACA;AArBA;;;;;;;AA8BO,SAAS,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAiB;IACvE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEpC,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF,IACA,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,mKAAA,CAAA,8BAA2B;IAC/C;IAGF,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACjC,MAAM,WAAW,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YAC9D,MAAM,WAAW,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;YAE5D,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;gBACtC,aAAa,UAAU;YACzB;QACF;IACF;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBAAO,8OAAC,gIAAA,CAAA,aAAU;;;;;IACpB;IAEA,MAAM,iBAAiB,kBACrB,8OAAC,2JAAA,CAAA,aAAU;YACT,SAAS;YACT,oBAAoB,2JAAA,CAAA,gBAAa;YACjC,WAAW;sBAEX,cAAA,8OAAC,mKAAA,CAAA,kBAAe;gBAAC,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;gBAAG,UAAU,mKAAA,CAAA,8BAA2B;0BACvF,cAAA,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC,8HAAA,CAAA,WAAQ;4BAEP,MAAM;4BACN,YAAY;4BACZ,MAAM;2BAHD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;IAWxB,MAAM,iBAAiB,kBACrB,8OAAC,2JAAA,CAAA,aAAU;YACT,SAAS;YACT,oBAAoB,2JAAA,CAAA,gBAAa;YACjC,WAAW;sBAEX,cAAA,8OAAC,mKAAA,CAAA,kBAAe;gBAAC,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;0BAC/C,cAAA,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC;4BAAkB,WAAU;sCAC3B,cAAA,8OAAC,8HAAA,CAAA,WAAQ;gCACP,MAAM;gCACN,YAAY;gCACZ,MAAM;gCACN,OAAO;;;;;;2BALD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;IAc3B,MAAM,mBAAmB;QACvB,MAAM,gBAAgB;YACpB;gBAAE,KAAK;gBAAW,OAAO;gBAAO,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAAW;YACjF;gBAAE,KAAK;gBAAe,OAAO;gBAAO,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAAe;YACzF;gBAAE,KAAK;gBAAa,OAAO;gBAAO,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAAa;SACtF;QAED,qBACE,8OAAC;YAAI,WAAU;sBACZ,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC;oBAAqB,WAAU;8BAC9B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCACX,OAAO,KAAK;kDACb,8OAAC;wCAAK,WAAU;kDACb,OAAO,KAAK,CAAC,MAAM;;;;;;;;;;;;0CAGxB,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA,qBAChB,8OAAC;4CAAkB,WAAU;sDAC3B,cAAA,8OAAC,8HAAA,CAAA,WAAQ;gDACP,MAAM;gDACN,YAAY;gDACZ,MAAM;gDACN,OAAO;;;;;;2CALD,KAAK,EAAE;;;;;oCASlB,OAAO,KAAK,CAAC,MAAM,KAAK,mBACvB,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;;;;;;;;;;;;;mBApBjE,OAAO,GAAG;;;;;;;;;;IA8B5B;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,SAAS,UAAU;YACnB,SAAS,UAAU;YACnB,SAAS,YAAY;;;;;;;AAG5B", "debugId": null}}, {"offset": {"line": 3195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/AddTodoModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Calendar, Flag, Tag, Folder, Plus, Minus } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { Category, Tag as TagType, Priority, TodoStatus } from '@/types/todo';\nimport { generateId } from '@/lib/utils';\n\ninterface AddTodoModalProps {\n  onClose: () => void;\n  categories: Category[];\n  tags: TagType[];\n}\n\nexport function AddTodoModal({ onClose, categories, tags }: AddTodoModalProps) {\n  const [title, setTitle] = useState('');\n  const [description, setDescription] = useState('');\n  const [priority, setPriority] = useState<Priority>('medium');\n  const [status, setStatus] = useState<TodoStatus>('pending');\n  const [categoryId, setCategoryId] = useState<string>('');\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [dueDate, setDueDate] = useState('');\n  const [dueTime, setDueTime] = useState('');\n  const [subtasks, setSubtasks] = useState<Array<{ title: string; completed: boolean }>>([]);\n  const [newSubtask, setNewSubtask] = useState('');\n\n  const { addTodo, settings } = useTodoStore();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!title.trim()) return;\n\n    let dueDateObj: Date | undefined;\n    if (dueDate) {\n      dueDateObj = new Date(dueDate + (dueTime ? `T${dueTime}` : 'T09:00'));\n    }\n\n    const subtaskObjects = subtasks.map(st => ({\n      id: generateId(),\n      title: st.title,\n      completed: st.completed,\n      createdAt: new Date(),\n      order: subtasks.indexOf(st),\n    }));\n\n    addTodo({\n      title: title.trim(),\n      description: description.trim() || undefined,\n      status,\n      priority,\n      categoryId: categoryId || undefined,\n      tags: selectedTags,\n      dueDate: dueDateObj,\n      isArchived: false,\n      subtasks: subtaskObjects,\n      attachments: [],\n      reminders: [],\n    });\n\n    onClose();\n  };\n\n  const handleAddSubtask = () => {\n    if (newSubtask.trim()) {\n      setSubtasks([...subtasks, { title: newSubtask.trim(), completed: false }]);\n      setNewSubtask('');\n    }\n  };\n\n  const handleRemoveSubtask = (index: number) => {\n    setSubtasks(subtasks.filter((_, i) => i !== index));\n  };\n\n  const handleToggleTag = (tagId: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tagId)\n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    );\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            添加新任务\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Title */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              任务标题 *\n            </label>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"输入任务标题...\"\n              required\n            />\n          </div>\n\n          {/* Description */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              描述\n            </label>\n            <textarea\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"添加任务描述...\"\n            />\n          </div>\n\n          {/* Priority and Status */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                优先级\n              </label>\n              <select\n                value={priority}\n                onChange={(e) => setPriority(e.target.value as Priority)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"low\">低</option>\n                <option value=\"medium\">中</option>\n                <option value=\"high\">高</option>\n                <option value=\"urgent\">紧急</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                状态\n              </label>\n              <select\n                value={status}\n                onChange={(e) => setStatus(e.target.value as TodoStatus)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"pending\">待处理</option>\n                <option value=\"in-progress\">进行中</option>\n                <option value=\"completed\">已完成</option>\n                <option value=\"cancelled\">已取消</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              分类\n            </label>\n            <select\n              value={categoryId}\n              onChange={(e) => setCategoryId(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">选择分类...</option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.icon} {category.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Tags */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              标签\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {tags.map(tag => (\n                <button\n                  key={tag.id}\n                  type=\"button\"\n                  onClick={() => handleToggleTag(tag.id)}\n                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                    selectedTags.includes(tag.id)\n                      ? 'text-white'\n                      : 'text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'\n                  }`}\n                  style={{\n                    backgroundColor: selectedTags.includes(tag.id) ? tag.color : undefined,\n                  }}\n                >\n                  {tag.name}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Due Date */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                截止日期\n              </label>\n              <input\n                type=\"date\"\n                value={dueDate}\n                onChange={(e) => setDueDate(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                截止时间\n              </label>\n              <input\n                type=\"time\"\n                value={dueTime}\n                onChange={(e) => setDueTime(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Subtasks */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              子任务\n            </label>\n            <div className=\"space-y-2\">\n              {subtasks.map((subtask, index) => (\n                <div key={index} className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"text\"\n                    value={subtask.title}\n                    onChange={(e) => {\n                      const updated = [...subtasks];\n                      updated[index].title = e.target.value;\n                      setSubtasks(updated);\n                    }}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => handleRemoveSubtask(index)}\n                    className=\"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900 rounded-lg\"\n                  >\n                    <Minus className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              ))}\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newSubtask}\n                  onChange={(e) => setNewSubtask(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddSubtask())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"添加子任务...\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={handleAddSubtask}\n                  className=\"p-2 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg\"\n                >\n                  <Plus className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\"\n            >\n              取消\n            </button>\n            <button\n              type=\"submit\"\n              className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n            >\n              创建任务\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AANA;;;;;;AAcO,SAAS,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAqB;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgD,EAAE;IACzF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEzC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,IAAI;QACJ,IAAI,SAAS;YACX,aAAa,IAAI,KAAK,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,GAAG,QAAQ;QACrE;QAEA,MAAM,iBAAiB,SAAS,GAAG,CAAC,CAAA,KAAM,CAAC;gBACzC,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,OAAO,GAAG,KAAK;gBACf,WAAW,GAAG,SAAS;gBACvB,WAAW,IAAI;gBACf,OAAO,SAAS,OAAO,CAAC;YAC1B,CAAC;QAED,QAAQ;YACN,OAAO,MAAM,IAAI;YACjB,aAAa,YAAY,IAAI,MAAM;YACnC;YACA;YACA,YAAY,cAAc;YAC1B,MAAM;YACN,SAAS;YACT,YAAY;YACZ,UAAU;YACV,aAAa,EAAE;YACf,WAAW,EAAE;QACf;QAEA;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,IAAI,IAAI;YACrB,YAAY;mBAAI;gBAAU;oBAAE,OAAO,WAAW,IAAI;oBAAI,WAAW;gBAAM;aAAE;YACzE,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC9C;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,SACzB;mBAAI;gBAAM;aAAM;IAExB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAI3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;gDAAyB,OAAO,SAAS,EAAE;;oDACzC,SAAS,IAAI;oDAAC;oDAAE,SAAS,IAAI;;+CADnB,SAAS,EAAE;;;;;;;;;;;;;;;;;sCAQ9B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,gBAAgB,IAAI,EAAE;4CACrC,WAAW,CAAC,6DAA6D,EACvE,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,eACA,0GACJ;4CACF,OAAO;gDACL,iBAAiB,aAAa,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,KAAK,GAAG;4CAC/D;sDAEC,IAAI,IAAI;2CAZJ,IAAI,EAAE;;;;;;;;;;;;;;;;sCAmBnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,WAAU;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDACC,MAAK;wDACL,OAAO,QAAQ,KAAK;wDACpB,UAAU,CAAC;4DACT,MAAM,UAAU;mEAAI;6DAAS;4DAC7B,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK;4DACrC,YAAY;wDACd;wDACA,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,oBAAoB;wDACnC,WAAU;kEAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;+CAhBX;;;;;sDAoBZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,kBAAkB;oDAC/E,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3795, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/SettingsModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Moon, Sun, Monitor } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\n\ninterface SettingsModalProps {\n  onClose: () => void;\n}\n\nexport function SettingsModal({ onClose }: SettingsModalProps) {\n  const { settings, updateSettings } = useTodoStore();\n  const [localSettings, setLocalSettings] = useState(settings);\n\n  const handleSave = () => {\n    updateSettings(localSettings);\n    onClose();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            设置\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        <div className=\"p-4 space-y-6\">\n          {/* Theme */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              主题\n            </h3>\n            <div className=\"grid grid-cols-3 gap-2\">\n              {[\n                { value: 'light', label: '浅色', icon: Sun },\n                { value: 'dark', label: '深色', icon: Moon },\n                { value: 'system', label: '系统', icon: Monitor },\n              ].map(theme => {\n                const Icon = theme.icon;\n                return (\n                  <button\n                    key={theme.value}\n                    onClick={() => setLocalSettings({ ...localSettings, theme: theme.value as any })}\n                    className={`p-3 rounded-lg border-2 transition-colors ${\n                      localSettings.theme === theme.value\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\n                    }`}\n                  >\n                    <Icon className=\"w-5 h-5 mx-auto mb-1 text-gray-600 dark:text-gray-400\" />\n                    <div className=\"text-xs text-gray-700 dark:text-gray-300\">\n                      {theme.label}\n                    </div>\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Default View */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              默认视图\n            </h3>\n            <select\n              value={localSettings.defaultView}\n              onChange={(e) => setLocalSettings({ ...localSettings, defaultView: e.target.value as any })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"list\">列表视图</option>\n              <option value=\"grid\">网格视图</option>\n              <option value=\"kanban\">看板视图</option>\n            </select>\n          </div>\n\n          {/* Default Priority */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              默认优先级\n            </h3>\n            <select\n              value={localSettings.defaultPriority}\n              onChange={(e) => setLocalSettings({ ...localSettings, defaultPriority: e.target.value as any })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"low\">低</option>\n              <option value=\"medium\">中</option>\n              <option value=\"high\">高</option>\n              <option value=\"urgent\">紧急</option>\n            </select>\n          </div>\n\n          {/* Options */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              选项\n            </h3>\n            \n            <label className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                显示已完成的任务\n              </span>\n              <input\n                type=\"checkbox\"\n                checked={localSettings.showCompletedTasks}\n                onChange={(e) => setLocalSettings({ ...localSettings, showCompletedTasks: e.target.checked })}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n            </label>\n\n            <label className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                启用通知\n              </span>\n              <input\n                type=\"checkbox\"\n                checked={localSettings.enableNotifications}\n                onChange={(e) => setLocalSettings({ ...localSettings, enableNotifications: e.target.checked })}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n            </label>\n\n            <label className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                自动归档已完成任务\n              </span>\n              <input\n                type=\"checkbox\"\n                checked={localSettings.autoArchiveCompleted}\n                onChange={(e) => setLocalSettings({ ...localSettings, autoArchiveCompleted: e.target.checked })}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n            </label>\n          </div>\n\n          {/* Auto Archive Days */}\n          {localSettings.autoArchiveCompleted && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                自动归档天数\n              </label>\n              <input\n                type=\"number\"\n                min=\"1\"\n                max=\"365\"\n                value={localSettings.autoArchiveDays}\n                onChange={(e) => setLocalSettings({ ...localSettings, autoArchiveDays: parseInt(e.target.value) || 30 })}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          )}\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n              工作时间\n            </h3>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                  开始时间\n                </label>\n                <input\n                  type=\"time\"\n                  value={localSettings.workingHours.start}\n                  onChange={(e) => setLocalSettings({\n                    ...localSettings,\n                    workingHours: { ...localSettings.workingHours, start: e.target.value }\n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                  结束时间\n                </label>\n                <input\n                  type=\"time\"\n                  value={localSettings.workingHours.end}\n                  onChange={(e) => setLocalSettings({\n                    ...localSettings,\n                    workingHours: { ...localSettings.workingHours, end: e.target.value }\n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-2 p-4 border-t border-gray-200 dark:border-gray-700\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\"\n          >\n            取消\n          </button>\n          <button\n            onClick={handleSave}\n            className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n          >\n            保存设置\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAUO,SAAS,cAAc,EAAE,OAAO,EAAsB;IAC3D,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa;QACjB,eAAe;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAS,OAAO;4CAAM,MAAM,gMAAA,CAAA,MAAG;wCAAC;wCACzC;4CAAE,OAAO;4CAAQ,OAAO;4CAAM,MAAM,kMAAA,CAAA,OAAI;wCAAC;wCACzC;4CAAE,OAAO;4CAAU,OAAO;4CAAM,MAAM,wMAAA,CAAA,UAAO;wCAAC;qCAC/C,CAAC,GAAG,CAAC,CAAA;wCACJ,MAAM,OAAO,MAAM,IAAI;wCACvB,qBACE,8OAAC;4CAEC,SAAS,IAAM,iBAAiB;oDAAE,GAAG,aAAa;oDAAE,OAAO,MAAM,KAAK;gDAAQ;4CAC9E,WAAW,CAAC,0CAA0C,EACpD,cAAc,KAAK,KAAK,MAAM,KAAK,GAC/B,gDACA,yFACJ;;8DAEF,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAI,WAAU;8DACZ,MAAM,KAAK;;;;;;;2CAVT,MAAM,KAAK;;;;;oCActB;;;;;;;;;;;;sCAKJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCACC,OAAO,cAAc,WAAW;oCAChC,UAAU,CAAC,IAAM,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wCAAQ;oCACzF,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCACC,OAAO,cAAc,eAAe;oCACpC,UAAU,CAAC,IAAM,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAAQ;oCAC7F,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAIrE,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;sDAG3D,8OAAC;4CACC,MAAK;4CACL,SAAS,cAAc,kBAAkB;4CACzC,UAAU,CAAC,IAAM,iBAAiB;oDAAE,GAAG,aAAa;oDAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC3F,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;sDAG3D,8OAAC;4CACC,MAAK;4CACL,SAAS,cAAc,mBAAmB;4CAC1C,UAAU,CAAC,IAAM,iBAAiB;oDAAE,GAAG,aAAa;oDAAE,qBAAqB,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC5F,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;sDAG3D,8OAAC;4CACC,MAAK;4CACL,SAAS,cAAc,oBAAoB;4CAC3C,UAAU,CAAC,IAAM,iBAAiB;oDAAE,GAAG,aAAa;oDAAE,sBAAsB,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC7F,WAAU;;;;;;;;;;;;;;;;;;wBAMf,cAAc,oBAAoB,kBACjC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO,cAAc,eAAe;oCACpC,UAAU,CAAC,IAAM,iBAAiB;4CAAE,GAAG,aAAa;4CAAE,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAAG;oCACtG,WAAU;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAsD;;;;;;8DAGvE,8OAAC;oDACC,MAAK;oDACL,OAAO,cAAc,YAAY,CAAC,KAAK;oDACvC,UAAU,CAAC,IAAM,iBAAiB;4DAChC,GAAG,aAAa;4DAChB,cAAc;gEAAE,GAAG,cAAc,YAAY;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACvE;oDACA,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAsD;;;;;;8DAGvE,8OAAC;oDACC,MAAK;oDACL,OAAO,cAAc,YAAY,CAAC,GAAG;oDACrC,UAAU,CAAC,IAAM,iBAAiB;4DAChC,GAAG,aAAa;4DAChB,cAAc;gEAAE,GAAG,cAAc,YAAY;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE;oDACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 4321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/components/TodoApp.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { Header } from './Header';\nimport { Sidebar } from './Sidebar';\nimport { TodoList } from './TodoList';\nimport { AddTodoModal } from './AddTodoModal';\nimport { SettingsModal } from './SettingsModal';\nimport { filterTodos, sortTodos } from '@/lib/utils';\n\nexport function TodoApp() {\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  const {\n    todos,\n    categories,\n    tags,\n    filter,\n    sort,\n    settings,\n    selectedTodos,\n    clearSelection,\n  } = useTodoStore();\n\n  // Filter and sort todos\n  const filteredTodos = filterTodos(todos, filter);\n  const sortedTodos = sortTodos(filteredTodos, sort.field, sort.direction);\n\n  // Apply theme\n  useEffect(() => {\n    const root = document.documentElement;\n    if (settings.theme === 'dark') {\n      root.classList.add('dark');\n    } else if (settings.theme === 'light') {\n      root.classList.remove('dark');\n    } else {\n      // System theme\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n      if (mediaQuery.matches) {\n        root.classList.add('dark');\n      } else {\n        root.classList.remove('dark');\n      }\n    }\n  }, [settings.theme]);\n\n  // Clear selection when clicking outside\n  const handleBackdropClick = () => {\n    if (selectedTodos.length > 0) {\n      clearSelection();\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-50 dark:bg-gray-900 transition-colors\">\n      {/* Sidebar */}\n      <div\n        className={`${\n          sidebarOpen ? 'w-80' : 'w-0'\n        } transition-all duration-300 overflow-hidden border-r border-gray-200 dark:border-gray-700`}\n      >\n        <Sidebar\n          categories={categories}\n          tags={tags}\n          onClose={() => setSidebarOpen(false)}\n        />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col min-w-0\">\n        {/* Header */}\n        <Header\n          onAddTodo={() => setShowAddModal(true)}\n          onOpenSettings={() => setShowSettingsModal(true)}\n          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}\n          sidebarOpen={sidebarOpen}\n          selectedCount={selectedTodos.length}\n        />\n\n        {/* Todo List */}\n        <main\n          className=\"flex-1 overflow-hidden\"\n          onClick={handleBackdropClick}\n        >\n          <TodoList\n            todos={sortedTodos}\n            categories={categories}\n            tags={tags}\n            view={settings.defaultView}\n          />\n        </main>\n      </div>\n\n      {/* Modals */}\n      {showAddModal && (\n        <AddTodoModal\n          onClose={() => setShowAddModal(false)}\n          categories={categories}\n          tags={tags}\n        />\n      )}\n\n      {showSettingsModal && (\n        <SettingsModal\n          onClose={() => setShowSettingsModal(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,KAAK,EACL,UAAU,EACV,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,aAAa,EACb,cAAc,EACf,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEf,wBAAwB;IACxB,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACzC,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,eAAe,KAAK,KAAK,EAAE,KAAK,SAAS;IAEvE,cAAc;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,SAAS,KAAK,KAAK,QAAQ;YAC7B,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO,IAAI,SAAS,KAAK,KAAK,SAAS;YACrC,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB,OAAO;YACL,eAAe;YACf,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,IAAI,WAAW,OAAO,EAAE;gBACtB,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;QACF;IACF,GAAG;QAAC,SAAS,KAAK;KAAC;IAEnB,wCAAwC;IACxC,MAAM,sBAAsB;QAC1B,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,GACT,cAAc,SAAS,MACxB,0FAA0F,CAAC;0BAE5F,cAAA,8OAAC,6HAAA,CAAA,UAAO;oBACN,YAAY;oBACZ,MAAM;oBACN,SAAS,IAAM,eAAe;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4HAAA,CAAA,SAAM;wBACL,WAAW,IAAM,gBAAgB;wBACjC,gBAAgB,IAAM,qBAAqB;wBAC3C,iBAAiB,IAAM,eAAe,CAAC;wBACvC,aAAa;wBACb,eAAe,cAAc,MAAM;;;;;;kCAIrC,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,8HAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,YAAY;4BACZ,MAAM;4BACN,MAAM,SAAS,WAAW;;;;;;;;;;;;;;;;;YAM/B,8BACC,8OAAC,kIAAA,CAAA,eAAY;gBACX,SAAS,IAAM,gBAAgB;gBAC/B,YAAY;gBACZ,MAAM;;;;;;YAIT,mCACC,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,SAAS,IAAM,qBAAqB;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 4462, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/keith/todolist/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { TodoApp } from '@/components/TodoApp';\n\nexport default function Home() {\n  return <TodoApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,6HAAA,CAAA,UAAO;;;;;AACjB", "debugId": null}}]}